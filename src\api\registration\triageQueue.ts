﻿import {useBaseApi} from '/@/api/base';

// 分诊队列接口服务
export const useTriageQueueApi = () => {
	const baseApi = useBaseApi("triageQueue");
	return {
		// 分页查询分诊队列
		page: baseApi.page,
		list: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "list",
                method: 'post',
                data,
            }, cancel);
        },
		// 查看分诊队列详细
		detail: baseApi.detail,
		// 新增分诊队列
		add: baseApi.add,
		// 更新分诊队列
		update: baseApi.update,
		// 删除分诊队列
		delete: baseApi.delete,
		// 批量删除分诊队列
		batchDelete: baseApi.batchDelete,
		// 导出分诊队列数据
		exportData: baseApi.exportData,
		// 导入分诊队列数据
		importData: baseApi.importData,
		// 下载分诊队列数据导入模板
		downloadTemplate: baseApi.downloadTemplate,

	}
}

// 分诊队列实体
export interface TriageQueue {
	// 主键Id
	id: number;
	// 排班计划ID
	schedulingPlanId: number;
	// 诊室ID
	roomId: number;
	// 诊室名称
	roomName: string;
	// 分诊台ID
	consoleId: number;
	// 分诊台名称
	consoleName: string;
	// 排队号
	queueNumber: number;
	// 状态（如排队中、已就诊等）
	status: number;
	// 时间段ID
	timePeriodId: number;
	// 时间段编码
	timePeriodCode: string;
	// 时间段名称
	timePeriodName: string;
	// 时间段开始时间
	timePeriodStartTime: string;
	// 时间段结束时间
	timePeriodEndTime: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}