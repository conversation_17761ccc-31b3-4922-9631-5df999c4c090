/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PrescriptionDetailOutput } from './prescription-detail-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListPrescriptionDetailOutput
 */
export interface SqlSugarPagedListPrescriptionDetailOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<PrescriptionDetailOutput>}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    items?: Array<PrescriptionDetailOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionDetailOutput
     */
    hasNextPage?: boolean;
}
