﻿import {useBaseApi} from '/@/api/base';

// 药品库存表接口服务
export const useDrugInventoryApi = () => {
	const baseApi = useBaseApi("drugInventory");
	return {
		// 分页查询药品库存表
		page: baseApi.page,
		// 查看药品库存表详细
		detail: baseApi.detail,
		// 新增药品库存表
		add: baseApi.add,
		// 更新药品库存表
		update: baseApi.update,
		// 删除药品库存表
		delete: baseApi.delete,
		// 批量删除药品库存表
		batchDelete: baseApi.batchDelete,
		// 导出药品库存表数据
		exportData: baseApi.exportData,
		// 导入药品库存表数据
		importData: baseApi.importData,
		// 下载药品库存表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		list: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "list",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 药品库存表实体
export interface DrugInventory {
	// 主键Id
	id: number;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 药品类型
	drugType: string;
	// 药房ID
	storageId: number;
	// 药房编码
	storageCode: string;
	// 药房名称
	storageName: string;
	// 规格
	spec: string;
	// 生产厂家ID
	manufacturerId: number;
	// 生产厂家名称
	manufacturerName: string;
	// 数量
	quantity: number;
	// 单位
	unit: string;
	// 待发药数量
	pendingQuantity: number;
	// 零售价
	salePrice: number;
	// 零售金额
	totalSalePrice: number;
	// 进价
	purchasePrice: number;
	// 采购金额
	totalPurchasePrice: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 有效期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 国家医保编码
	medicineCode: string;
	// 最后一次供应商ID
	lastSupplierId: number;
	// 最后一次供应商名称
	lastSupplierName: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}