﻿import {useBaseApi} from '/@/api/base';

// 处方类型接口服务
export const usePrescriptionTypeApi = () => {
	const baseApi = useBaseApi("prescriptionType");
	return {
		// 分页查询处方类型
		page: baseApi.page,
		// 查看处方类型详细
		detail: baseApi.detail,
		// 新增处方类型
		add: baseApi.add,
		// 更新处方类型
		update: baseApi.update,
		// 设置处方类型状态
		setStatus: baseApi.setStatus,
		// 删除处方类型
		delete: baseApi.delete,
		// 批量删除处方类型
		batchDelete: baseApi.batchDelete,
		// 导出处方类型数据
		exportData: baseApi.exportData,
		// 导入处方类型数据
		importData: baseApi.importData,
		// 下载处方类型数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 处方类型实体
export interface PrescriptionType {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 可使用的收费类别
	chargeCategorys: string;
	// 处方条目
	prescriptionEntries: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}