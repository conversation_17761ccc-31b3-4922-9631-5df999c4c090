﻿import {useBaseApi} from '/@/api/base';

// 中医证型接口服务
export const useTcmSyndromeApi = () => {
	const baseApi = useBaseApi("tcmSyndrome");
	return {
		// 分页查询中医证型
		page: baseApi.page,
		// 查看中医证型详细
		detail: baseApi.detail,
		// 新增中医证型
		add: baseApi.add,
		// 更新中医证型
		update: baseApi.update,
		// 设置中医证型状态
		setStatus: baseApi.setStatus,
		// 删除中医证型
		delete: baseApi.delete,
		// 批量删除中医证型
		batchDelete: baseApi.batchDelete,
		// 导出中医证型数据
		exportData: baseApi.exportData,
		// 导入中医证型数据
		importData: baseApi.importData,
		// 下载中医证型数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 中医证型实体
export interface TcmSyndrome {
	// 主键Id
	id: number;
	// 中医证型编码
	tcmSyndromeCode?: string;
	// 中医证型名称
	tcmSyndromeName?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 版本
	version: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 租户Id
	tenantId: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}