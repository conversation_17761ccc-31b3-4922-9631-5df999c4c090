/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemOutput } from './charge-item-output';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目输出参数
 *
 * @export
 * @interface ChargeItemOutput
 */
export interface ChargeItemOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    wubiCode?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    unit?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    spec?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    price?: number | null;

    /**
     * 进价
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    purchasePrice?: number | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    model?: string | null;

    /**
     * 批件产品名称
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    approvalName?: string | null;

    /**
     * 产地
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    producer?: string | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    manufacturer?: string | null;

    /**
     * 注册证号
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    registrationNumber?: string | null;

    /**
     * 物价编码
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    priceCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    chargeCategoryId?: number | null;

    /**
     * 收费类别 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    chargeCategoryFkDisplayName?: string | null;

    /**
     * 核算类别
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    calculateCategoryId?: number | null;

    /**
     * 核算类别 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    calculateCategoryFkDisplayName?: string | null;

    /**
     * 电子发票费用类别
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    dzfpChargeCategory?: string | null;

    /**
     * 病案首页费用类别
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    basyChargeCategory?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemOutput
     */
    highValue?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemOutput
     */
    useSeparately?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemOutput
     */
    uploadDw?: YesNoEnum;

    /**
     * 频次
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    frequencyId?: number | null;

    /**
     * 频次 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    frequencyFkDisplayName?: string | null;

    /**
     * 样本类型
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    sampleType?: string | null;

    /**
     * 护理等级
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    nurseLevel?: string | null;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    checkCategoryFkDisplayName?: string | null;

    /**
     * 退费模式
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    refundMode?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemOutput
     */
    _package?: YesNoEnum;

    /**
     * 使用科室
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    useDepts?: number | null;

    /**
     * 使用科室 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    useDeptsFkDisplayName?: string | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof ChargeItemOutput
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeItemOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItemOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItemOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItemOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    tenantId?: number | null;

    /**
     * 检查部位
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    checkPointId?: number | null;

    /**
     * 检查部位 描述
     *
     * @type {string}
     * @memberof ChargeItemOutput
     */
    checkPointFkDisplayName?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ChargeItemOutput
     */
    quantity?: number | null;

    /**
     * 套餐所包含的收费项目
     *
     * @type {Array<ChargeItemOutput>}
     * @memberof ChargeItemOutput
     */
    chargeItemPacks?: Array<ChargeItemOutput> | null;
}
