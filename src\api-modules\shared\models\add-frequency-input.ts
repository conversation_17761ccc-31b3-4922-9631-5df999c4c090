/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 频次增加输入参数
 *
 * @export
 * @interface AddFrequencyInput
 */
export interface AddFrequencyInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddFrequencyInput
     */
    name: string;

    /**
     * 时间间隔
     *
     * @type {number}
     * @memberof AddFrequencyInput
     */
    timeInterval: number;

    /**
     * 时间单位
     *
     * @type {number}
     * @memberof AddFrequencyInput
     */
    timeUnit: number;

    /**
     * 执行频率
     *
     * @type {number}
     * @memberof AddFrequencyInput
     */
    executionFrequency: number;

    /**
     * 执行时间
     *
     * @type {string}
     * @memberof AddFrequencyInput
     */
    executionTime: string;

    /**
     * 持续标识
     *
     * @type {number}
     * @memberof AddFrequencyInput
     */
    sustain: number;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof AddFrequencyInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * @type {StatusEnum}
     * @memberof AddFrequencyInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddFrequencyInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddFrequencyInput
     */
    remark?: string | null;
}
