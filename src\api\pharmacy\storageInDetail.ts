﻿import {useBaseApi} from '/@/api/base';

// 药品入库明细接口服务
export const useStorageInDetailApi = () => {
	const baseApi = useBaseApi("storageInDetail");
	return {
		// 分页查询药品入库明细
		page: baseApi.page,
		// 查看药品入库明细详细
		detail: baseApi.detail,
		// 新增药品入库明细
		add: baseApi.add,
		// 更新药品入库明细
		update: baseApi.update,
		// 删除药品入库明细
		delete: baseApi.delete,
		// 批量删除药品入库明细
		batchDelete: baseApi.batchDelete,
		// 导出药品入库明细数据
		exportData: baseApi.exportData,
		// 导入药品入库明细数据
		importData: baseApi.importData,
		// 下载药品入库明细数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
 
	}
}

// 药品入库明细实体
export interface StorageInDetail {
	// 主键Id
	id: number;
	// 药品类型
	drugType: string;
	// 入库单号
	storageInNo: string;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 进价
	purchasePrice: number;
	// 零售价
	salePrice: number;
	// 总进价
	totalPurchasePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 数量
	quantity: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 过期日期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 国家医保编码
	medicineCode: string;
	// 质量状况
	qualityStatus: string;
	// 验收状态
	acceptanceStatus: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}