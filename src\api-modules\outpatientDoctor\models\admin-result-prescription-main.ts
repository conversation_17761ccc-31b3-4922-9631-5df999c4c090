/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PrescriptionMain } from './prescription-main';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultPrescriptionMain
 */
export interface AdminResultPrescriptionMain {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultPrescriptionMain
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultPrescriptionMain
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultPrescriptionMain
     */
    message?: string | null;

    /**
     * @type {PrescriptionMain}
     * @memberof AdminResultPrescriptionMain
     */
    result?: PrescriptionMain;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultPrescriptionMain
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultPrescriptionMain
     */
    time?: Date;
}
