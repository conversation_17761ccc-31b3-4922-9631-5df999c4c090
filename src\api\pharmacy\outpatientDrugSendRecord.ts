﻿import {useBaseApi} from '/@/api/base';

// 门诊发药记录表接口服务
export const useOutpatientDrugSendRecordApi = () => {
	const baseApi = useBaseApi("outpatientDrugSendRecord");
	return {
		// 分页查询门诊发药记录表
		page: baseApi.page,
		// 查看门诊发药记录表详细
		detail: baseApi.detail,
		// 新增门诊发药记录表
		add: baseApi.add,
		// 更新门诊发药记录表
		update: baseApi.update,
		// 删除门诊发药记录表
		delete: baseApi.delete,
		// 批量删除门诊发药记录表
		batchDelete: baseApi.batchDelete,
		// 导出门诊发药记录表数据
		exportData: baseApi.exportData,
		// 导入门诊发药记录表数据
		importData: baseApi.importData,
		// 下载门诊发药记录表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 门诊发药记录表实体
export interface OutpatientDrugSendRecord {
	// 主键Id
	id: number;
	// 发药单号
	sendNo: string;
	// 发药人ID
	sendUserId: number;
	// 发药人名称
	sendUserName: string;
	// 发药时间
	sendTime: string;
	// 审核人ID
	auditUserId: number;
	// 审核人名称
	auditUserName: string;
	// 审核时间
	auditTime: string;
	// 调配人员ID
	pickUserId: number;
	// 调配人员名称
	pickUserName: string;
	// 调配时间
	pickTime: string;
	// 核对人员ID
	checkUserId: number;
	// 核对人员名称
	checkUserName: string;
	// 核对时间
	checkTime: string;
	// 药房ID
	storageId: number;
	// 药房编码
	storageCode: string;
	// 患者ID
	patientId: number;
	// 患者名称
	patientName: string;
	// 挂号ID
	registerId: number;
	// 就诊号
	visitNo: string;
	// 处方ID
	prescriptionId: number;
	// 处方号
	prescriptionNo: string;
	// 处方时间
	prescriptionTime: string;
	// 处方类型
	prescriptionType: string;
	// 库存ID
	inventoryId: number;
	// 科室ID
	deptId: number;
	// 科室名称
	deptName: string;
	// 医生ID
	doctorId: number;
	// 医生名称
	doctorName: string;
	// 发票号
	invoiceNumber: string;
	// 收费人员ID
	chargeStaffId: number;
	// 收费ID
	chargeId: number;
	// 收费时间
	chargeTime: string;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 药品类型
	drugType: string;
	// 药品规格
	spec: string;
	// 药品单位
	unit: string;
	// 发药数量
	quantity: number;
	// 单次剂量
	singleDose: number;
	// 单次剂量单位
	singleDoseUnit: string;
	// 用药途径ID
	medicationRoutesId: number;
	// 用药频次ID
	frequencyId: number;
	// 用药天数
	medicationDays: string;
	// 零售价
	price: number;
	// 总零售价
	amount: number;
	// 草药付数
	herbsQuantity: number;
	// 煎药方法
	decoctionMethod: string;
	// 是否代煎
	isDecoction: number;
	// 退药数量
	refundQuantity: number;
	// 总退药金额
	refundAmount: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 有效期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 药品通用名编码
	medicineCode: string;
	// 生产厂家ID
	manufacturerId: number;
	// 生产厂家名称
	manufacturerName: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}