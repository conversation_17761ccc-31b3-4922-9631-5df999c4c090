import { useBase<PERSON><PERSON> } from '/@/api/base';

/**
 * 收款员结算接口服务
 */
export const useCashierSettlementApi = () => {
	const baseApi = useBaseApi('CashierSettlement');
	return {
		// 分页查询结算记录
		page: baseApi.page,

		/**
		 * 获取收款员统计数据
		 */
		getStatistics: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getStatistics',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 执行日结操作
		 */
		dailySettlement: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'dailySettlement',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 执行月结操作
		 */
		monthlySettlement: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'monthlySettlement',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 撤销结算
		 */
		cancelSettlement: function (id: number, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'cancelSettlement',
					method: 'post',
					data: { id },
				},
				cancel
			);
		},

		/**
		 * 数据校验
		 */
		validateData: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'validateData',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 数据补录
		 */
		supplementData: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'supplementData',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 冲正处理
		 */
		processReversal: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'processReversal',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 重新计算结算数据
		 */
		recalculateSettlement: function (id: number, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'recalculateSettlement',
					method: 'post',
					data: { id },
				},
				cancel
			);
		},

		/**
		 * 获取操作日志
		 */
		getOperationLogs: function (settlementId: number, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getOperationLogs',
					method: 'post',
					data: { settlementId },
				},
				cancel
			);
		},

		/**
		 * 导出统计数据
		 */
		exportStatistics: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'exportStatistics',
					method: 'post',
					data,
					responseType: 'blob',
				},
				cancel
			);
		},
	};
};
