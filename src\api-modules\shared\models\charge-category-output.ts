/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费类别输出参数
 *
 * @export
 * @interface ChargeCategoryOutput
 */
export interface ChargeCategoryOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    wubiCode?: string | null;

    /**
     * 提成
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    commission?: number | null;

    /**
     * 记账属性
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    accountAttribute?: number | null;

    /**
     * 类型
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    type?: number | null;

    /**
     * 医保类型
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    medInsType?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeCategoryOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeCategoryOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeCategoryOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeCategoryOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeCategoryOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeCategoryOutput
     */
    remark?: string | null;
}
