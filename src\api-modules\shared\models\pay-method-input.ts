/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 门诊支付类型分页查询输入参数
 *
 * @export
 * @interface PayMethodInput
 */
export interface PayMethodInput {

    /**
     * @type {Search}
     * @memberof PayMethodInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PayMethodInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PayMethodInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PayMethodInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PayMethodInput
     */
    name?: string | null;
}
