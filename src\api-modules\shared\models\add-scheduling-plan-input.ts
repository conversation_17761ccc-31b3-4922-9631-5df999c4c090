/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 排班计划增加输入参数
 *
 * @export
 * @interface AddSchedulingPlanInput
 */
export interface AddSchedulingPlanInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddSchedulingPlanInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddSchedulingPlanInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddSchedulingPlanInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddSchedulingPlanInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddSchedulingPlanInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    tenantId?: number | null;

    /**
     * 医生id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    doctorId?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    timePeriodId?: number | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    regCategoryId?: number | null;

    /**
     * 限号数
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    regLimit?: number | null;

    /**
     * 限预约号数
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    appLimit?: number | null;

    /**
     * 已挂号数
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    regNumber?: number | null;

    /**
     * 已预约号数
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    appNumber?: number | null;

    /**
     * 门诊日期
     *
     * @type {Date}
     * @memberof AddSchedulingPlanInput
     */
    outpatientDate?: Date;

    /**
     * 科室id
     *
     * @type {number}
     * @memberof AddSchedulingPlanInput
     */
    deptId?: number | null;

    /**
     * 星期几
     *
     * @type {string}
     * @memberof AddSchedulingPlanInput
     */
    weekDay?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddSchedulingPlanInput
     */
    remark?: string | null;
}
