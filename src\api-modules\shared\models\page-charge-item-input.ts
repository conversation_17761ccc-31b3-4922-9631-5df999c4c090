/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目分页查询输入参数
 *
 * @export
 * @interface PageChargeItemInput
 */
export interface PageChargeItemInput {

    /**
     * @type {Search}
     * @memberof PageChargeItemInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageChargeItemInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageChargeItemInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageChargeItemInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageChargeItemInput
     */
    name?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof PageChargeItemInput
     */
    chargeCategoryId?: number | null;

    /**
     * 使用科室
     *
     * @type {number}
     * @memberof PageChargeItemInput
     */
    useDepts?: number | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageChargeItemInput
     */
    selectKeyList?: Array<number> | null;

    /**
     * @type {YesNoEnum}
     * @memberof PageChargeItemInput
     */
    _package?: YesNoEnum;
}
