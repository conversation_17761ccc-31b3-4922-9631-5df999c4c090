/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 频次分页查询输入参数
 *
 * @export
 * @interface PageFrequencyInput
 */
export interface PageFrequencyInput {

    /**
     * @type {Search}
     * @memberof PageFrequencyInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageFrequencyInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageFrequencyInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageFrequencyInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageFrequencyInput
     */
    name?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageFrequencyInput
     */
    selectKeyList?: Array<number> | null;
}
