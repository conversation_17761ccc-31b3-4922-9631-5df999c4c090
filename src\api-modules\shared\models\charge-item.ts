/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目表
 *
 * @export
 * @interface ChargeItem
 */
export interface ChargeItem {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItem
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItem
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItem
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItem
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItem
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof ChargeItem
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ChargeItem
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeItem
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeItem
     */
    wubiCode?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ChargeItem
     */
    unit?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ChargeItem
     */
    spec?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ChargeItem
     */
    price?: number | null;

    /**
     * 进价
     *
     * @type {number}
     * @memberof ChargeItem
     */
    purchasePrice?: number | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ChargeItem
     */
    model?: string | null;

    /**
     * 批件产品名称
     *
     * @type {string}
     * @memberof ChargeItem
     */
    approvalName?: string | null;

    /**
     * 产地
     *
     * @type {string}
     * @memberof ChargeItem
     */
    producer?: string | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ChargeItem
     */
    manufacturer?: string | null;

    /**
     * 注册证号
     *
     * @type {string}
     * @memberof ChargeItem
     */
    registrationNumber?: string | null;

    /**
     * 物价编码
     *
     * @type {string}
     * @memberof ChargeItem
     */
    priceCode?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    chargeCategoryId?: number | null;

    /**
     * 核算类别Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    calculateCategoryId?: number | null;

    /**
     * 电子发票费用类别
     *
     * @type {string}
     * @memberof ChargeItem
     */
    dzfpChargeCategory?: string | null;

    /**
     * 病案首页费用类别
     *
     * @type {string}
     * @memberof ChargeItem
     */
    basyChargeCategory?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItem
     */
    highValue?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItem
     */
    useSeparately?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItem
     */
    uploadDw?: YesNoEnum;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    frequencyId?: number | null;

    /**
     * 样本类型
     *
     * @type {string}
     * @memberof ChargeItem
     */
    sampleType?: string | null;

    /**
     * 护理等级
     *
     * @type {string}
     * @memberof ChargeItem
     */
    nurseLevel?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    checkCategoryId?: number | null;

    /**
     * 检查部位Id
     *
     * @type {number}
     * @memberof ChargeItem
     */
    checkPointId?: number | null;

    /**
     * 退费模式 0正常 1申请 2 审核
     *
     * @type {number}
     * @memberof ChargeItem
     */
    refundMode?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItem
     */
    _package?: YesNoEnum;

    /**
     * 使用科室 无表示对应全部科室
     *
     * @type {number}
     * @memberof ChargeItem
     */
    useDepts?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof ChargeItem
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeItem
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeItem
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeItem
     */
    orderNo?: number | null;
}
