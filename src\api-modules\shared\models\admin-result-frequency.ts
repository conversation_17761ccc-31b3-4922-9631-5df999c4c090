/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Frequency } from './frequency';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultFrequency
 */
export interface AdminResultFrequency {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultFrequency
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultFrequency
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultFrequency
     */
    message?: string | null;

    /**
     * @type {Frequency}
     * @memberof AdminResultFrequency
     */
    result?: Frequency;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultFrequency
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultFrequency
     */
    time?: Date;
}
