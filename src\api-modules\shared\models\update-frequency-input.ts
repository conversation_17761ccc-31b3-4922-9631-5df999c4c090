/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 频次更新输入参数
 *
 * @export
 * @interface UpdateFrequencyInput
 */
export interface UpdateFrequencyInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateFrequencyInput
     */
    name: string;

    /**
     * 时间间隔
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    timeInterval: number;

    /**
     * 时间单位
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    timeUnit: number;

    /**
     * 执行频率
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    executionFrequency: number;

    /**
     * 执行时间
     *
     * @type {string}
     * @memberof UpdateFrequencyInput
     */
    executionTime: string;

    /**
     * 持续标识
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    sustain: number;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof UpdateFrequencyInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * @type {StatusEnum}
     * @memberof UpdateFrequencyInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateFrequencyInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateFrequencyInput
     */
    remark?: string | null;
}
