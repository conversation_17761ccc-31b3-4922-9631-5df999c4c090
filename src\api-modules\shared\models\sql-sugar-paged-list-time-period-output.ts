/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TimePeriodOutput } from './time-period-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListTimePeriodOutput
 */
export interface SqlSugarPagedListTimePeriodOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<TimePeriodOutput>}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    items?: Array<TimePeriodOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTimePeriodOutput
     */
    hasNextPage?: boolean;
}
