﻿import { useBaseApi } from '/@/api/base';

// 检查部位接口服务
export const useCheckPointApi = () => {
	const baseApi = useBaseApi('checkPoint');
	return {
		// 分页查询检查部位
		page: baseApi.page,
		// 查看检查部位详细
		detail: baseApi.detail,
		// 查询检查部位列表
		list: function (cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'list',
					method: 'get',
				},
				cancel
			);
		},
		// 新增检查部位
		add: baseApi.add,
		// 更新检查部位
		update: baseApi.update,
		// 设置检查部位状态
		setStatus: baseApi.setStatus,
		// 删除检查部位
		delete: baseApi.delete,
		// 批量删除检查部位
		batchDelete: baseApi.batchDelete,
		// 导出检查部位数据
		exportData: baseApi.exportData,
		// 导入检查部位数据
		importData: baseApi.importData,
		// 下载检查部位数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	};
};

// 检查部位实体
export interface CheckPoint {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 检查类别
	checkCategoryId: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}
