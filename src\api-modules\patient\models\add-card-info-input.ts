/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { BusinessTypeEnum } from './business-type-enum';
import { CardStatusEnum } from './card-status-enum';
import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 就诊卡管理增加输入参数
 *
 * @export
 * @interface AddCardInfoInput
 */
export interface AddCardInfoInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddCardInfoInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddCardInfoInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddCardInfoInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    tenantId?: number | null;

    /**
     * 患者唯一号
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    patientNo?: string | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    name?: string | null;

    /**
     * 英文姓名
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    englishName?: string | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    wubiCode?: string | null;

    /**
     * @type {GenderEnum}
     * @memberof AddCardInfoInput
     */
    sex?: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    age?: number;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    ageUnit?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof AddCardInfoInput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof AddCardInfoInput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    idCardNo?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    nation?: string | null;

    /**
     * 电话号码
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    phone?: string | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    contactName?: string | null;

    /**
     * 联系人关系
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    contactRelationship?: string | null;

    /**
     * 联系人地址
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    contactAddress?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    contactPhone?: string | null;

    /**
     * 国籍
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    nationality?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    occupation?: string | null;

    /**
     * 婚姻
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    marriage?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    nativePlaceCounty?: number | null;

    /**
     * 出生地省
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    birthplaceProvince?: number | null;

    /**
     * 出生地市
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    birthplaceCity?: number | null;

    /**
     * 出生地县
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    birthplaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    residenceProvince?: number;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    residenceCity?: number;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    residenceCounty?: number;

    /**
     * 详细现居住地
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    residenceAddress?: string | null;

    /**
     * 工作地址省
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    workProvince?: number | null;

    /**
     * 工作地址市
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    workCity?: number | null;

    /**
     * 工作地址县
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    workCounty?: number | null;

    /**
     * 详细工作地址
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    workAddress?: string | null;

    /**
     * 工作单位
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    workPlace?: string | null;

    /**
     * 单位电话
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    workPlacePhone?: string | null;

    /**
     * 医保类别
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    medInsCategory?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof AddCardInfoInput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * 医疗类别（费别）
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    medCategory?: number | null;

    /**
     * 险种类型
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    insuranceType?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof AddCardInfoInput
     */
    isNoCard?: YesNoEnum;

    /**
     * 医保卡信息
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    medInsCardInfo?: string | null;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    cardNo?: string | null;

    /**
     * 患者ID
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    patientId?: number;

    /**
     * @type {BusinessTypeEnum}
     * @memberof AddCardInfoInput
     */
    businessType?: BusinessTypeEnum;

    /**
     * 使用科室
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    useDepts?: string | null;

    /**
     * 充值方式
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    chargeModes?: string | null;

    /**
     * 余额
     *
     * @type {number}
     * @memberof AddCardInfoInput
     */
    balance?: number | null;

    /**
     * @type {CardStatusEnum}
     * @memberof AddCardInfoInput
     */
    status?: CardStatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddCardInfoInput
     */
    remark?: string | null;
}
