﻿import {useBaseApi} from '/@/api/base';

// 药理分类维护接口服务
export const usePharmacologicalClassApi = () => {
	const baseApi = useBaseApi("pharmacologicalClass");
	return {
		// 分页查询药理分类维护
		page: baseApi.page,
		// 查看药理分类维护详细
		detail: baseApi.detail,
		// 新增药理分类维护
		add: baseApi.add,
		// 更新药理分类维护
		update: baseApi.update,
		// 设置药理分类维护状态
		setStatus: baseApi.setStatus,
		// 删除药理分类维护
		delete: baseApi.delete,
		// 批量删除药理分类维护
		batchDelete: baseApi.batchDelete,
		// 导出药理分类维护数据
		exportData: baseApi.exportData,
		// 导入药理分类维护数据
		importData: baseApi.importData,
		// 下载药理分类维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 药理分类维护实体
export interface PharmacologicalClass {
	// 主键Id
	id: number;
	// 药理分类编码
	classCode: string;
	// 药理分类名称
	className: string;
	// 父级分类ID
	parentId: number;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}