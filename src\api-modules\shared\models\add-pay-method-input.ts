/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 门诊支付类型增加输入参数
 *
 * @export
 * @interface AddPayMethodInput
 */
export interface AddPayMethodInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddPayMethodInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddPayMethodInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddPayMethodInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    code?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    wubiCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    remark?: string | null;

    /**
     * 挂号价格
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    regPrice?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof AddPayMethodInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddPayMethodInput
     */
    orderNo?: number | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    name: string;

    /**
     * 类型
     *
     * @type {string}
     * @memberof AddPayMethodInput
     */
    type: string;
}
