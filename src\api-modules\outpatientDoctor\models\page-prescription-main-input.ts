/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 处方主表分页查询输入参数
 *
 * @export
 * @interface PagePrescriptionMainInput
 */
export interface PagePrescriptionMainInput {

    /**
     * @type {Search}
     * @memberof PagePrescriptionMainInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PagePrescriptionMainInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    descStr?: string | null;

    /**
     * 处方号
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    prescriptionNo?: string | null;

    /**
     * 处方时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionMainInput
     */
    prescriptionTimeRange?: Array<Date> | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    prescriptionType?: string | null;

    /**
     * 处方名称
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    prescriptionName?: string | null;

    /**
     * 西药处方类型
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    wstrnMdcnPrescriptionType?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    patientName?: string | null;

    /**
     * 挂号Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    registerId?: number | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    billingDeptId?: number | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生签名
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    billingDoctorSign?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionMainInput
     */
    chargeTimeRange?: Array<Date> | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionMainInput
     */
    refundTimeRange?: Array<Date> | null;

    /**
     * 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    status?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    remark?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnosticName?: string | null;

    /**
     * 次诊断1编码
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnostic1Code?: string | null;

    /**
     * 次诊断1名称
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnostic1Name?: string | null;

    /**
     * 次诊断2编码
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnostic2Code?: string | null;

    /**
     * 次诊断2名称
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    diagnostic2Name?: string | null;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    tcmDiagnosticCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    tcmDiagnosticName?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    isPrint?: number | null;

    /**
     * 中药付数
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    herbsQuantity?: number | null;

    /**
     * 中药煎法
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    herbsDecoction?: string | null;

    /**
     * 是否代煎
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    isDecoction?: number | null;

    /**
     * 打印时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionMainInput
     */
    printTimeRange?: Array<Date> | null;

    /**
     * 收费主表Id
     *
     * @type {number}
     * @memberof PagePrescriptionMainInput
     */
    chargeMainId?: number | null;

    /**
     * 退费发票号
     *
     * @type {string}
     * @memberof PagePrescriptionMainInput
     */
    refundInvoiceNumber?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PagePrescriptionMainInput
     */
    selectKeyList?: Array<number> | null;
}
