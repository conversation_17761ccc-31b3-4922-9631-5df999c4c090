/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedCategoryEnum } from './med-category-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 费用类别更新输入参数
 *
 * @export
 * @interface UpdateFeeCategoryInput
 */
export interface UpdateFeeCategoryInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateFeeCategoryInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateFeeCategoryInput
     */
    name: string;

    /**
     * @type {MedCategoryEnum}
     * @memberof UpdateFeeCategoryInput
     */
    medCategory: MedCategoryEnum;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof UpdateFeeCategoryInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * 医保id
     *
     * @type {number}
     * @memberof UpdateFeeCategoryInput
     */
    medInsId?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof UpdateFeeCategoryInput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * @type {StatusEnum}
     * @memberof UpdateFeeCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateFeeCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateFeeCategoryInput
     */
    remark?: string | null;
}
