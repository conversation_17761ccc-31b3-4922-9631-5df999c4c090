/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查部位更新输入参数
 *
 * @export
 * @interface UpdateCheckPointInput
 */
export interface UpdateCheckPointInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateCheckPointInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateCheckPointInput
     */
    name: string;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof UpdateCheckPointInput
     */
    checkCategoryId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateCheckPointInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateCheckPointInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateCheckPointInput
     */
    remark?: string | null;
}
