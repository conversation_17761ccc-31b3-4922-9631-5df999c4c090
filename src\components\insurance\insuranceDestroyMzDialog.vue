<template>
	<div class="charge-container">
		<el-dialog v-model="state.isShowDialog" :width="850" height="100%" draggable="" :close-on-click-modal="false"
			@close="closeDialog">
			<template #header>
				<div style="color: #fff">
					<!--<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>-->
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules"
				v-loading="state.loading">
				<el-row :gutter="15">
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="医疗统筹类别" prop="p_yltclb">
							<el-select v-model="state.ruleForm.p_yltclb" filterable remote reserve-keyword
								placeholder="医疗统筹类别" remote-show-suffix>
								<el-option label="住院" value="'1'" />
								<el-option label="家庭病床" value="'2'" />
								<el-option label="急诊转住院" value="'3'" />
								<el-option label="门诊统筹" value="'4'" />
								<el-option label="意外伤害" value="'5'" />
								<el-option label="普通门诊" value="6" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb10">
						<el-form-item label="认证方式" prop="p_sfrzfs">
							<el-radio-group v-model="state.ruleForm.p_sfrzfs">
								<el-radio :label="'03'">有卡</el-radio>
								<el-radio :label="'00'">无卡</el-radio>
								<el-radio :label="'01'">电子凭证</el-radio>
								<el-radio :label="'05'">刷脸</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="险种标志" prop="p_xzbz">
							<el-select v-model="state.ruleForm.p_xzbz" filterable remote reserve-keyword
								placeholder="请选择险种标志" remote-show-suffix>
								<el-option label="医疗" value="C" />
								<el-option label="工伤" value="D" />
								<el-option label="生育" value="E" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="电子凭证" prop="p_ewm">
							<el-input v-model="state.ruleForm.p_ewm" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4" class="mb10">

						<el-button type="success" @click="getPatientInfo()">获取医保患者信息</el-button>

					</el-col>
				</el-row>

				<el-divider style="margin: 5px" />
		

			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" :disabled="state.loading">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<InsurancePreSettlementDialog ref="insurancePreSettlementRef" :title="'医保预结算'"
			@settlementFinish="settlementFinish" />
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
<script lang="ts" setup name="charge">
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';

import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useBaseApi } from '/@/api/base';
import InsurancePreSettlementDialog from './insurancePreSettlementDialog.vue';
import {
	InsuranceRegister,
	SettleMzPreRequest,
	SettleMzPreBcxm,
	SettleMzPreJbbm,
	SettleMzPreFypd
} from '/@/models/insurance';
import { forEach } from 'lodash-es';
// API服务
const insurancePatientApi = useBaseApi("insurancePatient");
const basicInfoApi = useBasicInfoApi();

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
const insurancePreSettlementRef = ref<InstanceType<typeof InsurancePreSettlementDialog>>();
//父级传递来的函数，用于回调
const emit = defineEmits(['registerCancel', 'registerFinish', 'settlementFinish']);

const ruleFormRef = ref();

const state = reactive({
	loading: false,

	isShowDialog: false,

	ruleForm: {
		p_sfrzfs: "00", // 默认有卡
	} as any,
	register: {} as InsuranceRegister, //挂号信息
});
 
// 打开弹窗
const openDialog = async (register: InsuranceRegister) => {



	// 重置表单
	ruleFormRef.value?.resetFields();

	state.ruleForm.p_yltclb = register.insurance.medicalPoolingCategory;
	state.ruleForm.p_xzbz = register.insurance.medicalInsuranceFlag;
	state.register = register;
	state.ruleForm.idCardNo = register.idCardNo;
	state.ruleForm.xm = register.name;
	state.ruleForm.diagnosticCode = register.diagnosticCode ?? '';
	state.ruleForm.diagnosticName = register.diagnosticName ?? '';

	// 诊断
	if (register.diagList.length > 0) {
		state.icd10Data = []
		state.ruleForm.diagnosticCode = register.diagList[0].diagCode;
		state.ruleForm.diagnosticName = register.diagList[0].diagName;
	}
 
	state.isShowDialog = true;
 
 
};
const convertChargeItems = (chargeItems: any[]) => {
	state.items = chargeItems;

};
 
// 关闭弹窗
const closeDialog = () => {

	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	emit('registerCancel', state.ruleForm);
	closeDialog();
};
 
// 结算完成
const settlementFinish = (result: any) => {
	closeDialog();
	emit('settlementFinish', state.register, result);

};
// 提交

const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {

			let params = buildSettlePreParams();
			console.log('提交医保患者信息登记 params=', params);
			//跳转到预结算
			insurancePreSettlementRef.value?.openDialog(state.register, params);
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};
const buildSettlePreParams = () => {
	//预结算参数
	let params: SettleMzPreRequest = {
		p_yltclb: state.ruleForm.p_yltclb,
		p_grbh: state.ruleForm.grbh,
		p_xm: state.ruleForm.xm,
		p_xb: state.ruleForm.xb,
		p_blh: state.register.outpatientNo,
		//p_fyrq: '',
		p_ysbm: '',// 医生编码
		p_xzbz: state.ruleForm.xzbz,
		p_jylb: state.ruleForm.jylb,//外地就医类别
		p_jyyybm: state.ruleForm.ydjyyybm,//外地就医类别
		p_kh: '',
		p_ptmzskbz: '',
		p_jbbm: state.ruleForm.diagnosticCode,
		p_jbsm: state.ruleForm.jbsm,
		p_ewm: state.ruleForm.p_ewm, // 电子凭证
		p_authno: state.ruleForm.p_ewm, //  刷脸授权码（医保三类终端刷脸返回的 authNo） 
		p_ylzcbs: state.ruleForm.p_ylzcbs, // 医疗政策标识
		p_zyzd: state.ruleForm.diagnosticCode,//主要诊断（跨省异地门诊大病结算必传）
		p_sfrzfs: state.ruleForm.p_sfrzfs, // 收费认证方式
		p_mzjzzzbz: state.ruleForm.p_mzjzzzbz, // 门诊急诊转诊标识
		p_wsbz: state.ruleForm.p_wsbz, // 外伤标识
		p_sjdsfbz: state.ruleForm.p_sjdsfbz, // 涉及第三方标识
		p_yltclbmx: '',
		p_ksbm: String(state.register.deptId), // 开单科室编码
	}
	//
	params.p_kh = '';
	params.p_ptmzskbz = '';
	params.p_yltclb = state.ruleForm.p_yltclb;
	if (params.p_jylb === '10')// 异地治疗
	{

	}
	// 补充项目信息（通过 get_bcxm 获取需录入项）
	params.p_bcxm_ds = [{ bcxmbh: '', bcxmz: '' } as SettleMzPreBcxm];
	// 多诊断
	params.p_jbbm_ds = [] as SettleMzPreJbbm[];
	for (let i = 0; i < state.register.diagList.length; i++) {
		const item = state.register.diagList[i];
		params.p_jbbm_ds.push({
			dzdjbbm: item.diagCode,
			maindiag_flag: item.isMainDiag, // 主诊断标志
			diag_type: item.diagType, // 诊断类型
			diag_srt_no: item.orderNo, // 诊断顺序号
			diag_dept: item.deptId, // 诊断科室
			diag_dr_code: item.doctorId, // 诊断医生编码
			diag_dr_name: item.doctorName, // 诊断医生姓名
			diag_time: item.diagTime, // 诊断日期
		} as SettleMzPreJbbm);
	}
	//费用
	params.p_fypd_ds = [] as SettleMzPreFypd[];
	// for (let i = 0; i < state.register.chargeList.length; i++) {
	// 	const item = state.register.chargeList[i];
	// 	params.p_fypd_ds.push({
	// 		// 根据实际需要映射字段，以下为示例字段
	// 		yyxmbm: item.itemCode,
	// 		yyxmmc: item.itemName,
	// 		dj: item.price,
	// 		zje: item.amount,
	// 		sl: item.quantity,
	// 		zxksbm: item.executingDeptId,
	// 		kdksbm: item.billingDeptId,
	// 		gg: item.spec,
	// 		bzsl: 0, // 大包装的小包装数量
	// 		yyts: item.medicationDays,
	// 		yysm: item.remark, // 用药说明
	// 		yzlsh: item.billingDetailId,// 医嘱流水号
	// 		sfryxm: '',
	// 		gytj: '',
	// 		yypc: '',// 用药频次
	// 		dcyl: item.singleDose, // 单次用量
	// 		zxysbm: item.executeDoctorId, // 执行医师编码
	// 		kdysbm: item.billingDoctorId, // 开单医师编码
	// 		clbz: '0',
	// 		sxzfbl: 0, // 自付比例
	// 		// ... 其他需要的字段
	// 	} as SettleMzPreFypd);
	// }
	return params;
};
///
const getPatientInfo = async () => {

	console.log('获取医保患者信息 state.ruleForm.acquisitionMethod=', state.register.acquisitionMethod);
	if (state.register.acquisitionMethod === '03') {
		// 有卡
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	}
	else if (state.register.acquisitionMethod === '01') {
		// 电子凭证
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	} else if (state.register.acquisitionMethod === '02') {
		// 刷脸
		state.ruleForm.sbjgmc = '';
		state.ruleForm.sbjgbh = '';
	} else // if (state.register.acquisitionMethod === '00') {
	{	// 无卡
		state.loading = true;
		await insurancePatientApi.post("QueryBasicInfo", {
			p_grbh: state.ruleForm.idCardNo,
			p_xzbz: state.ruleForm.p_xzbz,
			p_xm: state.register.name,
			p_yltclb: state.ruleForm.p_yltclb
		}).then((res) => {
			Object.assign(state.ruleForm, res.data.result);
			console.log(res.data.result, state.ruleForm);
		}).finally(() => {
			// 关闭加载状态
			state.loading = false;
		});

	}

};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
