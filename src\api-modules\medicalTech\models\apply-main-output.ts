/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单输出参数
 *
 * @export
 * @interface ApplyMainOutput
 */
export interface ApplyMainOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    id?: number;

    /**
     * 申请单号
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    applyNo?: string | null;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    registerId?: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    visitNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    patientId?: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    itemCode?: string | null;

    /**
     * 药品或单项名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    number?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    flag?: number | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    urgencyLevel?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    prescriptionType?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    checkCategoryName?: string | null;

    /**
     * 检查部位id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    checkPointId?: number | null;

    /**
     * 检查部位名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    checkPointName?: string | null;

    /**
     * 检查目的
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    checkObjective?: string | null;

    /**
     * 症状
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    symptom?: string | null;

    /**
     * 药物过敏
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    drugAllergy?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    diagnosticName?: string | null;

    /**
     * 其他诊断编码
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    otherDiagnosticCode?: string | null;

    /**
     * 其他诊断名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    otherDiagnosticName?: string | null;

    /**
     * 病情介绍
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    introduction?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    nationalstandardCode?: string | null;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    createOrgName?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ApplyMainOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ApplyMainOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    remark?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof ApplyMainOutput
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof ApplyMainOutput
     */
    specimenName?: string | null;
}
