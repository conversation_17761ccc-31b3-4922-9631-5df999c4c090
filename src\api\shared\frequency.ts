﻿import {useBaseApi} from '/@/api/base';

// 频次接口服务
export const useFrequencyApi = () => {
	const baseApi = useBaseApi("frequency");
	return {
		// 分页查询频次
		page: baseApi.page,
		// 查看频次详细
		detail: baseApi.detail,
		// 新增频次
		add: baseApi.add,
		// 更新频次
		update: baseApi.update,
		// 设置频次状态
		setStatus: baseApi.setStatus,
		// 删除频次
		delete: baseApi.delete,
		// 批量删除频次
		batchDelete: baseApi.batchDelete,
		// 导出频次数据
		exportData: baseApi.exportData,
		// 导入频次数据
		importData: baseApi.importData,
		// 下载频次数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 频次实体
export interface Frequency {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 时间间隔
	timeInterval?: number;
	// 时间单位
	timeUnit?: string;
	// 执行频率
	executionFrequency?: number;
	// 执行时间
	executionTime?: string;
	// 持续标识
	sustain?: string;
	// 使用范围
	usageScope?: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}