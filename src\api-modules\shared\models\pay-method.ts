/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 支付方式表
 *
 * @export
 * @interface PayMethod
 */
export interface PayMethod {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof PayMethod
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PayMethod
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PayMethod
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PayMethod
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PayMethod
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PayMethod
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PayMethod
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PayMethod
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PayMethod
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PayMethod
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PayMethod
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof PayMethod
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof PayMethod
     */
    wubiCode?: string | null;

    /**
     * 类型
     *
     * @type {string}
     * @memberof PayMethod
     */
    type?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PayMethod
     */
    remark?: string | null;

    /**
     * 挂号价格
     *
     * @type {number}
     * @memberof PayMethod
     */
    regPrice?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof PayMethod
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof PayMethod
     */
    orderNo?: number | null;
}
