/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListMedicationRoutesOutput } from './sql-sugar-paged-list-medication-routes-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListMedicationRoutesOutput
 */
export interface AdminResultSqlSugarPagedListMedicationRoutesOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListMedicationRoutesOutput}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    result?: SqlSugarPagedListMedicationRoutesOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListMedicationRoutesOutput
     */
    time?: Date;
}
