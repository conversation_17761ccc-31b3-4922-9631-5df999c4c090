/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemPackOutput } from './charge-item-pack-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultListChargeItemPackOutput
 */
export interface AdminResultListChargeItemPackOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultListChargeItemPackOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultListChargeItemPackOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultListChargeItemPackOutput
     */
    message?: string | null;

    /**
     * 数据
     *
     * @type {Array<ChargeItemPackOutput>}
     * @memberof AdminResultListChargeItemPackOutput
     */
    result?: Array<ChargeItemPackOutput> | null;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultListChargeItemPackOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultListChargeItemPackOutput
     */
    time?: Date;
}
