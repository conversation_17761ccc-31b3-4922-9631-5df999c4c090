﻿import { useBaseApi } from '/@/api/base';

// 检查接口服务
export const useExaminationApi = () => {
	const baseApi = useBaseApi('examination');
	return {
		// 分页查询检查
		page: baseApi.page,
		// 查看检查详细
		detail: baseApi.detail,
		// 新增检查
		add: baseApi.add,
		// 更新检查
		update: baseApi.update,
		// 删除检查
		delete: baseApi.delete,
		//删除检查明细
		deleteDetail: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'deleteDetail',
					method: 'post',
					data,
				},
				cancel
			);
		},
		// 批量删除检查
		batchDelete: baseApi.batchDelete,
	};
};

// 检查实体
export interface Examination {
	// 主键Id
	id: number;
	// 申请单号
	applyNo: string;
	// 就诊Id
	registerId: number;
	// 就诊流水号
	visitNo: string;
	//门诊号
	outpatientNo?: string | null;
	//就诊卡号
	cardNo?: string | null;
	// 患者Id
	patientId: number;
	// 患者姓名
	patientName: string;
	// 检查类别Id
	checkCategoryId?: number;
	// 检查类别名称
	checkCategoryName?: string;
	// 检查部位Id
	checkPointId: number;
	// 检查部位名称
	checkPointName: string;
	// 检查目的
	checkObjective: string;
	// 临床诊断
	clinicalDiagnosis: string;
	// 病历摘要
	medicalRecordSummary: string;
	// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
	status: number;
	// 备注
	remark: string;
	// 门诊住院标识
	flag: number;
	// 开单时间
	billingTime: string;
	// 开单科室Id
	billingDeptId: number;
	// 开单科室名称
	billingDeptName: string;
	// 开单医生Id
	billingDoctorId: number;
	// 开单医生名称
	billingDoctorName: string;
	// 执行时间
	executeTime: string;
	// 执行科室Id
	executeDeptId: number;
	// 执行科室名称
	executeDeptName: string;
	// 执行科室地址
	executeDeptAddress: string;
	// 执行医生Id
	executeDoctorId: number;
	// 执行医生名称
	executeDoctorName: string;
	// 收费人员Id
	chargeStaffId: number;
	// 收费人员名称
	chargeStaffName: string;
	// 收费时间
	chargeTime: string;
	// 医生签名
	doctorSign: string;
	// 医嘱Id
	medicalAdviceId: number;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 检查明细
	examinationDetails: Array<ExaminationDetails>;
}

// 检查明细实体
export interface ExaminationDetails {
	// 主键Id
	id: number;
	//检查Id
	examinationId: number;
	// 申请单号
	applyNo: string;
	// 项目Id
	itemId?: number;
	// 项目编码
	itemCode?: string | null;
	// 项目名称
	itemName?: string | null;
	// 单位
	unit: string;
	// 单价
	price: number;
	// 数量
	quantity: number;
	// 金额
	amount?: number | null;
	// 是否套餐
	isPackage: number;
	// 国家医保编码
	medicineCode: string;
	// 国标编码
	nationalstandardCode: string;
	// 收费类别Id
	chargeCategoryId: number;
	// 执行科室Id
	executeDeptId?: number | null;
	// 执行科室名称
	executeDeptName: string;
	// 自付比例
	selfPayRatio: number;
	// 自付比例是否审核 1审核 2不审核
	isRatioAudit: number;
	// 自付比例审核时间
	ratioAuditTime: string;
	// 自付比例审核人员Id
	ratioAuditStaffId: number;
	// 自付比例审核人员名称
	ratioAuditStaffName: string;
	// 备注
	remark: string;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	//套餐所包含的收费项目
	chargeItemPacks?: Array<any> | null;
}
// 检查明细套餐实体
export interface ExaminationPackageItem {
	// 主键Id
	id: number;
	// 申请单号
	applyNo: string;
	// 就诊Id
	registerId: number;
	// 就诊流水号
	visitNo: string;
	// 患者Id
	patientId: number;
	// 患者姓名
	patientName: string;
	// 检查类别Id
	checkCategoryId?: number;
	// 检查类别名称
	checkCategoryName?: string;
	// 检查部位Id
	checkPointId: number;
	// 检查部位名称
	checkPointName: string;
	// 检查目的
	checkObjective: string;
	// 临床诊断
	clinicalDiagnosis: string;
	// 病历摘要
	medicalRecordSummary: string;
	// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
	status: number;
	// 备注
	remark: string;
	// 门诊住院标识
	flag: number;
	// 开单时间
	billingTime: string;
	// 开单科室Id
	billingDeptId: number;
	// 开单科室名称
	billingDeptName: string;
	// 开单医生Id
	billingDoctorId: number;
	// 开单医生名称
	billingDoctorName: string;
	// 执行时间
	executeTime: string;
	// 执行科室Id
	executeDeptId: number;
	// 执行科室名称
	executeDeptName: string;
	// 执行科室地址
	executeDeptAddress: string;
	// 执行医生Id
	executeDoctorId: number;
	// 执行医生名称
	executeDoctorName: string;
	// 收费人员Id
	chargeStaffId: number;
	// 收费人员名称
	chargeStaffName: string;
	// 收费时间
	chargeTime: string;
	// 医生签名
	doctorSign: string;
	// 医嘱Id
	medicalAdviceId: number;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}
