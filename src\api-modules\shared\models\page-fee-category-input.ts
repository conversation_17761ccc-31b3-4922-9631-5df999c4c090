/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 费用类别分页查询输入参数
 *
 * @export
 * @interface PageFeeCategoryInput
 */
export interface PageFeeCategoryInput {

    /**
     * @type {Search}
     * @memberof PageFeeCategoryInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageFeeCategoryInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageFeeCategoryInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageFeeCategoryInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    descStr?: string | null;

    /**
     * 编号
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageFeeCategoryInput
     */
    name?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageFeeCategoryInput
     */
    selectKeyList?: Array<number> | null;
}
