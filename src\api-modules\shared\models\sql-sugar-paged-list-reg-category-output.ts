/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RegCategoryOutput } from './reg-category-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListRegCategoryOutput
 */
export interface SqlSugarPagedListRegCategoryOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<RegCategoryOutput>}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    items?: Array<RegCategoryOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListRegCategoryOutput
     */
    hasNextPage?: boolean;
}
