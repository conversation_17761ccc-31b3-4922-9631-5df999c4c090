import { useBase<PERSON><PERSON> } from '/@/api/base';

// 排班计划接口服务
export const useSchedulingPlanApi = () => {
	const baseApi = useBaseApi('schedulingPlan');
	return {
		// 分页查询排班计划
		page: baseApi.page,
		// 增加排班计划
		add: baseApi.add,
		// 删除排班计划
		delete: baseApi.delete,
		// 更新排班计划
		update: baseApi.update,
		// 根据门诊日期获取排班计划列表
		list: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'list',
					method: 'get',
					data,
				},
				cancel
			);
		},
		//医生排班计划
		DoctorPlanList : function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'DoctorPlanList',
					method: 'post',
					data,
				},
				cancel
			);
		},
		GetSchedulingDept : function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'GetSchedulingDept',
					method: 'post',
					data,
				},
				cancel
			);
		},
		GetSchedulingDoctor : function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'GetSchedulingDoctor',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//save
		SavePlan : function (data: any, cancel: boolean = false) {
		
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'SavePlan',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//保存为模板
		SaveTemplate : function (data: any, cancel: boolean = false) {
		
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'SaveTemplate',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//模板列表
		GetTemplateList : function (data: any, cancel: boolean = false) {
		
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'GetTemplateList',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//查询模板
		GetTemplate : function (data: any, cancel: boolean = false) {
		
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'GetTemplate',
					method: 'post',
					data,
				},
				cancel
			);
		}
	};
};
