/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddMedicationRoutesInput } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultMedicationRoutes } from '../models';
import { AdminResultSqlSugarPagedListMedicationRoutesOutput } from '../models';
import { DeleteMedicationRoutesInput } from '../models';
import { PageMedicationRoutesInput } from '../models';
import { UpdateMedicationRoutesInput } from '../models';
/**
 * MedicationRoutesApi - axios parameter creator
 * @export
 */
export const MedicationRoutesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加给药途径 ➕
         * @param {AddMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesAddPost: async (body?: AddMedicationRoutesInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除给药途径 ❌
         * @param {Array<DeleteMedicationRoutesInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesBatchDeletePost: async (body: Array<DeleteMedicationRoutesInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiMedicationRoutesBatchDeletePost.');
            }
            const localVarPath = `/api/medicationRoutes/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除给药途径 ❌
         * @param {DeleteMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesDeletePost: async (body?: DeleteMedicationRoutesInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取给药途径详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiMedicationRoutesDetailGet.');
            }
            const localVarPath = `/api/medicationRoutes/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出给药途径记录 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesExportPost: async (body?: PageMedicationRoutesInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载给药途径数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入给药途径记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询给药途径 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesPagePost: async (body?: PageMedicationRoutesInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新给药途径 ✏️
         * @param {UpdateMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMedicationRoutesUpdatePost: async (body?: UpdateMedicationRoutesInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/medicationRoutes/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MedicationRoutesApi - functional programming interface
 * @export
 */
export const MedicationRoutesApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加给药途径 ➕
         * @param {AddMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesAddPost(body?: AddMedicationRoutesInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除给药途径 ❌
         * @param {Array<DeleteMedicationRoutesInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesBatchDeletePost(body: Array<DeleteMedicationRoutesInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除给药途径 ❌
         * @param {DeleteMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesDeletePost(body?: DeleteMedicationRoutesInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取给药途径详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultMedicationRoutes>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出给药途径记录 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesExportPost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载给药途径数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入给药途径记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询给药途径 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesPagePost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListMedicationRoutesOutput>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新给药途径 ✏️
         * @param {UpdateMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesUpdatePost(body?: UpdateMedicationRoutesInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await MedicationRoutesApiAxiosParamCreator(configuration).apiMedicationRoutesUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * MedicationRoutesApi - factory interface
 * @export
 */
export const MedicationRoutesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加给药途径 ➕
         * @param {AddMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesAddPost(body?: AddMedicationRoutesInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除给药途径 ❌
         * @param {Array<DeleteMedicationRoutesInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesBatchDeletePost(body: Array<DeleteMedicationRoutesInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除给药途径 ❌
         * @param {DeleteMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesDeletePost(body?: DeleteMedicationRoutesInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取给药途径详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultMedicationRoutes>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出给药途径记录 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesExportPost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载给药途径数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入给药途径记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询给药途径 🔖
         * @param {PageMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesPagePost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListMedicationRoutesOutput>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新给药途径 ✏️
         * @param {UpdateMedicationRoutesInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMedicationRoutesUpdatePost(body?: UpdateMedicationRoutesInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return MedicationRoutesApiFp(configuration).apiMedicationRoutesUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MedicationRoutesApi - object-oriented interface
 * @export
 * @class MedicationRoutesApi
 * @extends {BaseAPI}
 */
export class MedicationRoutesApi extends BaseAPI {
    /**
     * 
     * @summary 增加给药途径 ➕
     * @param {AddMedicationRoutesInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesAddPost(body?: AddMedicationRoutesInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除给药途径 ❌
     * @param {Array<DeleteMedicationRoutesInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesBatchDeletePost(body: Array<DeleteMedicationRoutesInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除给药途径 ❌
     * @param {DeleteMedicationRoutesInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesDeletePost(body?: DeleteMedicationRoutesInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取给药途径详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultMedicationRoutes>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出给药途径记录 🔖
     * @param {PageMedicationRoutesInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesExportPost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载给药途径数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入给药途径记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询给药途径 🔖
     * @param {PageMedicationRoutesInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesPagePost(body?: PageMedicationRoutesInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListMedicationRoutesOutput>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新给药途径 ✏️
     * @param {UpdateMedicationRoutesInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MedicationRoutesApi
     */
    public async apiMedicationRoutesUpdatePost(body?: UpdateMedicationRoutesInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return MedicationRoutesApiFp(this.configuration).apiMedicationRoutesUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
