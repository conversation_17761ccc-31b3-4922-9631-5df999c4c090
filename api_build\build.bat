@echo off
CHCP 65001

set dir=%~dp0

set moduleName=apiServices
set apiServicesPath=%dir%..\src\api-services\
set apiUrl=http://localhost:5005/swagger/Default/swagger.json

if "%1"=="approvalFlow" (
  set moduleName=approvalFlow
  set apiServicesPath=%dir%..\src\api-plugins\approvalFlow\
  set apiUrl=http://localhost:5005/swagger/ApprovalFlow/swagger.json
) else if "%1"=="dingTalk" (
  set moduleName=dingTalk
  set apiServicesPath=%dir%..\src\api-plugins\dingTalk\
  set apiUrl=http://localhost:5005/swagger/DingTalk/swagger.json
) else if "%1"=="goView" (
  set moduleName=goView
  set apiServicesPath=%dir%..\src\api-plugins\goView\
  set apiUrl=http://localhost:5005/swagger/DingTalk/swagger.json
) else if "%1"=="patient" (
  set moduleName=patient
  set apiServicesPath=%dir%..\src\api-modules\patient\
  set apiUrl=http://localhost:5005/swagger/Patient/swagger.json
) else if "%1"=="shared" (
  set moduleName=shared
  set apiServicesPath=%dir%..\src\api-modules\shared\
  set apiUrl=http://localhost:5005/swagger/Shared/swagger.json
) else if "%1"=="registration" (
  set moduleName=registration
  set apiServicesPath=%dir%..\src\api-modules\registration\
  set apiUrl=http://localhost:5005/swagger/Registration/swagger.json
) else if "%1"=="outpatientDoctor" (
  set moduleName=outpatientDoctord
  set apiServicesPath=%dir%..\src\api-modules\outpatientDoctor\
  set apiUrl=http://localhost:5005/swagger/OutDoctor/swagger.json
) else if "%1"=="inpatient" (
  set moduleName=inpatient
  set apiServicesPath=%dir%..\src\api-modules\inpatient\
  set apiUrl=http://localhost:5005/swagger/Inpatient/swagger.json
) else if "%1"=="inpatientDoctor" (
  set moduleName=inpatientDoctor
  set apiServicesPath=%dir%..\src\api-modules\inpatientDoctor\
  set apiUrl=http://localhost:5005/swagger/InpatientDoctor/swagger.json
) else if "%1"=="pharmacy" (
  set moduleName=pharmacy
  set apiServicesPath=%dir%..\src\api-modules\pharmacy\
  set apiUrl=http://localhost:5005/swagger/Pharmacy/swagger.json
) else if "%1"=="medicalTech" (
  set moduleName=medicalTech
  set apiServicesPath=%dir%..\src\api-modules\medicalTech\
  set apiUrl=http://localhost:5005/swagger/MedicalTech/swagger.json
) else if "%1"=="financial" (
  set moduleName=financial
  set apiServicesPath=%dir%..\src\api-modules\financial\
  set apiUrl=http://localhost:5005/swagger/Financial/swagger.json
) else if "%1"=="report" (
  set moduleName=report
  set apiServicesPath=%dir%..\src\api-modules\report\
  set apiUrl=http://localhost:5005/swagger/Report/swagger.json
) else if "%1"=="nursing" (
  set moduleName=nursing
  set apiServicesPath=%dir%..\src\api-modules\nursing\
  set apiUrl=http://localhost:5005/swagger/Nursing/swagger.json
) else if "%1"=="insurance" (
  set moduleName=insurance
  set apiServicesPath=%dir%..\src\api-modules\insurance\
  set apiUrl=http://localhost:5005/swagger/Insurance/swagger.json
) 

if exist %apiServicesPath% (
    echo ================================ ???? %moduleName% ================================
    rd /s /q %apiServicesPath%
)

echo ================================ ???? %moduleName% ================================

java -jar %dir%swagger-codegen-cli.jar generate -i %apiUrl% -l typescript-axios -o %apiServicesPath%

@rem ????????????
rd /s /q %apiServicesPath%.swagger-codegen
del /q %apiServicesPath%.gitignore
del /q %apiServicesPath%.npmignore
del /q %apiServicesPath%.swagger-codegen-ignore
del /q %apiServicesPath%git_push.sh
del /q %apiServicesPath%package.json
del /q %apiServicesPath%README.md
del /q %apiServicesPath%tsconfig.json

echo ================================ ???? ================================