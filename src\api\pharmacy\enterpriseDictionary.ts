﻿import {useBaseApi} from '/@/api/base';

// 企业管理接口服务
export const useEnterpriseDictionaryApi = () => {
	const baseApi = useBaseApi("enterpriseDictionary");
	return {
		// 分页查询企业管理
		page: baseApi.page,
		// 查看企业管理详细
		detail: baseApi.detail,
		// 新增企业管理
		add: baseApi.add,
		// 更新企业管理
		update: baseApi.update,
		// 设置企业管理状态
		setStatus: baseApi.setStatus,
		// 删除企业管理
		delete: baseApi.delete,
		// 批量删除企业管理
		batchDelete: baseApi.batchDelete,
		// 导出企业管理数据
		exportData: baseApi.exportData,
		// 导入企业管理数据
		importData: baseApi.importData,
		// 下载企业管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 企业管理实体
export interface EnterpriseDictionary {
	// 主键Id
	id: number;
	// 企业编码
	enterpriseCode?: string;
	// 企业名称
	enterpriseName?: string;
	// 企业名称拼音
	enterpriseNamePinyin: string;
	// 企业类型
	enterpriseType?: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}