import {useBaseApi} from '/@/api/base';
 
// 药品退货接口服务
export const useOutpatientDrugSendApi = () => {
    const baseApi = useBaseApi("outpatientDrugSend");
    return {

        	
		add: baseApi.add,
        // 下载药品退货数据导入模板
        downloadTemplate: baseApi.downloadTemplate,
        getPatientInfo:async function (data: any, cancel: boolean = false) {
                  // 读取 mock 数据
                   // 使用 fetch 或 axios 请求 mock 数据
          const response = await fetch('/mock/patientInfo.json');
          const mockData = await response.json();
          console.log("mockData",mockData);
          return mockData;
            // return baseApi.request({
            //     url: baseApi.baseUrl + "getPatientInfo",
            //     method: 'post',
            //     data,
            // }, cancel);
        },
        getPrescriptionInfo: async function (data: any, cancel: boolean = false) {
 
                // 读取 mock 数据
                   // 使用 fetch 或 axios 请求 mock 数据
          const response = await fetch('/mock/prescription.json');
          const mockData = await response.json();
          console.log("mockData",mockData);
          return mockData;
       
            //     return baseApi.request({
            //         url: baseApi.baseUrl + "getPrescriptionInfo",
            //         method: 'post',
            //         data,
            //     }, cancel);
       
      
        },
        // 查询现有库存
        getDrugInventory: async function (data: any, cancel: boolean = false) {
          return baseApi.request({
                url: baseApi.baseUrl + "currentDrugInventory",
                method: 'post',
                data,
            }, cancel);
         },
 
  

        // 获取下拉列表数据
        getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
        submit: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'submit',
                method: 'post',
                data
            }, cancel);
        },
        page4Supplier: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "page4Supplier",
                method: 'post',
                data,
            }, cancel);
        },
    }
}
 