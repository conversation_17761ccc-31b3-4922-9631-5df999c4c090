﻿import {useBaseApi} from '/@/api/base';

// 药品库房维护接口服务
export const useDrugStorageApi = () => {
	const baseApi = useBaseApi("drugStorage");
	return {
		// 分页查询药品库房维护
		page: baseApi.page,
		// 查看药品库房维护详细
		detail: baseApi.detail,
		// 新增药品库房维护
		add: baseApi.add,
		// 更新药品库房维护
		update: baseApi.update,
		// 设置药品库房维护状态
		setStatus: baseApi.setStatus,
		// 删除药品库房维护
		delete: baseApi.delete,
		// 批量删除药品库房维护
		batchDelete: baseApi.batchDelete,
		// 导出药品库房维护数据
		exportData: baseApi.exportData,
		// 导入药品库房维护数据
		importData: baseApi.importData,
		// 下载药品库房维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 药品库房维护实体
export interface DrugStorage {
	// 主键Id
	id: number;
	// 库房编码
	storageCode: string;
	// 库房名称
	storageName: string;
	// 存储药品类型
	storageDrugType: string;
	// 库存金额上限
	storageAmountLimit: number;
	// 父级库房ID
	parentId: number;
	// 父级库房编码
	parentCode: string;
	// 服务对象
	serviceObject: number;
	// 采购入库审核
	purchaseAudit: number;
	// 采购退货审核
	purchaseReturnAudit: number;
	// 药店申领审核
	applyAudit: number;
	// 药店退药审核
	applyReturnAudit: number;
	// 出库审核
	outAudit: number;
	// 特殊处理审核
	specialAudit: number;
	// 按批号盘点
	batchCheck: number;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}