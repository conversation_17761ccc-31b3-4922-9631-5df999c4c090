﻿import {useBaseApi} from '/@/api/base';

// 药品退货接口服务
export const useStorageRefundRecordApi = () => {
	const baseApi = useBaseApi("storageRefundRecord");
	return {
		// 分页查询药品退货
		page: baseApi.page,
		// 查看药品退货详细
		detail: baseApi.detail,
		// 新增药品退货
		add: baseApi.add,
		// 更新药品退货
		update: baseApi.update,
		// 删除药品退货
		delete: baseApi.delete,
		// 批量删除药品退货
		batchDelete: baseApi.batchDelete,
		// 导出药品退货数据
		exportData: baseApi.exportData,
		// 导入药品退货数据
		importData: baseApi.importData,
		// 下载药品退货数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		submit: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'submit',
                method: 'post',
                data
            }, cancel);
        },
		page4Supplier: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "page4Supplier",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 药品退货实体
export interface StorageRefundRecord {
	// 主键Id
	id: number;
	// 退货单号
	refundNo: string;
	// 退货时间
	refundTime: string;
	// 退货类型
	refundType: string;
	// 退货原因
	refundReason: string;
	// 申请科室
	applyDeptId: number;
	// 申请部门编码
	applyDeptCode: string;
	// 申请部门名称
	applyDeptName: string;
	// 目标库房
	targetStorageId: number;
	// 目标库房编码
	targetStorageCode: string;
	// 目标库房名称
	targetStorageName: string;
	// 总零售价
	totalSalePrice: number;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}