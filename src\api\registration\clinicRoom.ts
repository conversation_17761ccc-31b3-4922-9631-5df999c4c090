﻿import {useBaseApi} from '/@/api/base';

// 诊室维护接口服务
export const useClinicRoomApi = () => {
	const baseApi = useBaseApi("clinicRoom");
	return {
		// 分页查询诊室维护
		page: baseApi.page,
		// 查看诊室维护详细
		detail: baseApi.detail,
		// 新增诊室维护
		add: baseApi.add,
		// 更新诊室维护
		update: baseApi.update,
		// 删除诊室维护
		delete: baseApi.delete,
		// 批量删除诊室维护
		batchDelete: baseApi.batchDelete,
		// 导出诊室维护数据
		exportData: baseApi.exportData,
		// 导入诊室维护数据
		importData: baseApi.importData,
		// 下载诊室维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		list: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "list",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 诊室维护实体
export interface ClinicRoom {
	// 主键Id
	id: number;
	// 诊室名称
	name: string;
	// 诊室编码
	code: string;
	// 科室id
	deptId: number;
	// 科室名称
	deptName: string;
	// ip地址
	ipAddress: string;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}