/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TcmSyndromeOutput } from './tcm-syndrome-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListTcmSyndromeOutput
 */
export interface SqlSugarPagedListTcmSyndromeOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<TcmSyndromeOutput>}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    items?: Array<TcmSyndromeOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTcmSyndromeOutput
     */
    hasNextPage?: boolean;
}
