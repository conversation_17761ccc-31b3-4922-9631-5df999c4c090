/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查类别更新输入参数
 *
 * @export
 * @interface UpdateCheckCategoryInput
 */
export interface UpdateCheckCategoryInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateCheckCategoryInput
     */
    id: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof UpdateCheckCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateCheckCategoryInput
     */
    name: string;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof UpdateCheckCategoryInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof UpdateCheckCategoryInput
     */
    wubiCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof UpdateCheckCategoryInput
     */
    chargeCategoryId: number;

    /**
     * @type {StatusEnum}
     * @memberof UpdateCheckCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateCheckCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateCheckCategoryInput
     */
    remark?: string | null;
}
