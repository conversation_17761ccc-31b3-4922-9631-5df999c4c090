/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListRegCategoryOutput } from './sql-sugar-paged-list-reg-category-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListRegCategoryOutput
 */
export interface AdminResultSqlSugarPagedListRegCategoryOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListRegCategoryOutput}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    result?: SqlSugarPagedListRegCategoryOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListRegCategoryOutput
     */
    time?: Date;
}
