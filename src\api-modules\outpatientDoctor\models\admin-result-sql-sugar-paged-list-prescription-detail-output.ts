/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListPrescriptionDetailOutput } from './sql-sugar-paged-list-prescription-detail-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListPrescriptionDetailOutput
 */
export interface AdminResultSqlSugarPagedListPrescriptionDetailOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListPrescriptionDetailOutput}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    result?: SqlSugarPagedListPrescriptionDetailOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListPrescriptionDetailOutput
     */
    time?: Date;
}
