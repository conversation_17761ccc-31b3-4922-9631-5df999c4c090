import { useBase<PERSON><PERSON> } from '/@/api/base';

// 基础信息服务接口
export const useBasicInfoApi = () => {
	const baseApi = useBaseApi('basicInfo');
	return {
		/**
		 * 获取科室列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getDepartments: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getDepartments',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取频次列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getFrequencies: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getFrequencies',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取核算类别列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getCalculateCategories: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getCalculateCategories',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取收费类别列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getChargeCategories: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getChargeCategories',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取检查类别列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getCheckCategories: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getCheckCategories',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取检查部位列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getCheckPoints: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getCheckPoints',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取费用类别列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getFeeCategories: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getFeeCategories',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取ICD10列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getIcd10s: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getIcd10s',
					method: 'get',
					data,
				},
				cancel
			);
		},
		//获取中医诊断列表
		GetTcmDiagnosis: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getTcmDiagnosis',
					method: 'get',
					data,
				},
				cancel
			);
		},
		//获取中医证型列表
		GetTcmSyndrome: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getTcmSyndrome',
					method: 'get',
					data,
				},
				cancel
			);
		},
		// 获取给药途径列表
		getMedicationRoutes: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getMedicationRoutes',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取支付方式列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getPayMethods: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getPayMethods',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取处方类型列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getPrescriptionTypes: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getPrescriptionTypes',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取挂号类别列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getRegCategories: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getRegCategories',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取用户列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getUsers: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getUsers',
					method: 'get',
					data,
				},
				cancel
			);
		},
			/**
		 * 获取门诊医生列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getOutpatientDoctor: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getOutpatientDoctor',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 获取行政区域树列表
		 * @param data
		 * @param cancel
		 * @returns
		 */
		getRegionTree: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getRegionTree',
					method: 'get',
					data,
				},
				cancel
			);
		},
		/**
		 * 根据结构获取科室
		 * @param data
		 * @param cancel
		 * @returns
		 */
		GetDeptByStructCode: function (structCode: any,keyword:any='', cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'GetDeptByStructCode?structCode=' + structCode + '&keyword=' + keyword,
					method: 'get' 
				},
				cancel
			);
		},
	};
};
