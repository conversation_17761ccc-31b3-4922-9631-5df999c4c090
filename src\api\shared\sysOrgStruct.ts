﻿import { treeNodeContentProps } from 'element-plus/es/components/tree-v2/src/virtual-tree';
import {useBaseApi} from '/@/api/base';

// 科室结构维护接口服务
export const useSysOrgStructApi = () => {
	const baseApi = useBaseApi("sysOrgStruct");
	return {
		// 分页查询科室结构维护
		page: baseApi.page,
		// 查看科室结构维护详细
		detail: baseApi.detail,
		// 新增科室结构维护
		add: baseApi.add,
		addDept: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "addDept",
                method: 'post',
                data,
            }, cancel);
        },
		// 更新科室结构维护
		update: baseApi.update,
		// 删除科室结构维护
		delete: baseApi.delete,
		deleteDept: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "deleteDept",
                method: 'post',
                data,
            }, cancel);
        },
		// 批量删除科室结构维护
		batchDelete: baseApi.batchDelete,
		// 导出科室结构维护数据
		exportData: baseApi.exportData,
		// 导入科室结构维护数据
		importData: baseApi.importData,
		// 下载科室结构维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		tree: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "tree",
                method: 'post',
                data,
            }, cancel);
        },
		//查询详情 
		deptPage: function (data: any, cancel: boolean = false) {
			return baseApi.request({
				url: baseApi.baseUrl + "deptPage",
				method: 'post',
				data,
			}, cancel);
		},// 获取科室列表
		getDeptList: function (data: any, cancel: boolean = false) {
			return baseApi.request({
				url: baseApi.baseUrl + "getDeptList",
				method: 'post',
				data,
			}, cancel);
		}
	}
}

// 科室结构维护实体
export interface SysOrgStruct {
	// 主键Id
	id: number;
	// 编号
	code: string;
	// 名称
	name: string;
	// 父级id
	parentId: number;
	// 父级名称
	parentName: string;
	// 层级
	level: number;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}