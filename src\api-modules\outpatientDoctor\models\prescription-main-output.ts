/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方主表输出参数
 *
 * @export
 * @interface PrescriptionMainOutput
 */
export interface PrescriptionMainOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    id?: number;

    /**
     * 处方号
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    prescriptionNo?: string | null;

    /**
     * 处方时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    prescriptionTime?: Date | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    prescriptionType?: string | null;

    /**
     * 处方名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    prescriptionName?: string | null;

    /**
     * 西药处方类型
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    wstrnMdcnPrescriptionType?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    patientName?: string | null;

    /**
     * 挂号Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    registerId?: number | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    billingDeptId?: number | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生签名
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    billingDoctorSign?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    refundTime?: Date | null;

    /**
     * 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    status?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    remark?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnosticName?: string | null;

    /**
     * 次诊断1编码
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnostic1Code?: string | null;

    /**
     * 次诊断1名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnostic1Name?: string | null;

    /**
     * 次诊断2编码
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnostic2Code?: string | null;

    /**
     * 次诊断2名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    diagnostic2Name?: string | null;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    tcmDiagnosticCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    tcmDiagnosticName?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    isPrint?: number | null;

    /**
     * 中药付数
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    herbsQuantity?: number | null;

    /**
     * 中药煎法
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    herbsDecoction?: string | null;

    /**
     * 是否代煎
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    isDecoction?: number | null;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    createOrgName?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionMainOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    tenantId?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof PrescriptionMainOutput
     */
    printTime?: Date | null;

    /**
     * 收费主表Id
     *
     * @type {number}
     * @memberof PrescriptionMainOutput
     */
    chargeMainId?: number | null;

    /**
     * 退费发票号
     *
     * @type {string}
     * @memberof PrescriptionMainOutput
     */
    refundInvoiceNumber?: string | null;
}
