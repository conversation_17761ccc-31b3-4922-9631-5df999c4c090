﻿import {useBaseApi} from '/@/api/base';

// 药品单位维护接口服务
export const useDrugUnitApi = () => {
	const baseApi = useBaseApi("drugUnit");
	return {
		// 分页查询药品单位维护
		page: baseApi.page,
		// 查看药品单位维护详细
		detail: baseApi.detail,
		// 新增药品单位维护
		add: baseApi.add,
		// 更新药品单位维护
		update: baseApi.update,
		// 设置药品单位维护状态
		setStatus: baseApi.setStatus,
		// 删除药品单位维护
		delete: baseApi.delete,
		// 批量删除药品单位维护
		batchDelete: baseApi.batchDelete,
		// 导出药品单位维护数据
		exportData: baseApi.exportData,
		// 导入药品单位维护数据
		importData: baseApi.importData,
		// 下载药品单位维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 药品单位维护实体
export interface DrugUnit {
	// 主键Id
	id: number;
	// 单位名称
	unit: string;
	// 转换单位
	convertUnit: string;
	// 转换比率
	convertRatio: number;
	// 单位拼音
	unitPinyin: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}