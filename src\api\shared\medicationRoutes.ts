﻿import {useBaseApi} from '/@/api/base';

// 给药途径接口服务
export const useMedicationRoutesApi = () => {
	const baseApi = useBaseApi("medicationRoutes");
	return {
		// 分页查询给药途径
		page: baseApi.page,
		// 查看给药途径详细
		detail: baseApi.detail,
		// 新增给药途径
		add: baseApi.add,
		// 更新给药途径
		update: baseApi.update,
		// 设置给药途径状态
		setStatus: baseApi.setStatus,
		// 删除给药途径
		delete: baseApi.delete,
		// 批量删除给药途径
		batchDelete: baseApi.batchDelete,
		// 导出给药途径数据
		exportData: baseApi.exportData,
		// 导入给药途径数据
		importData: baseApi.importData,
		// 下载给药途径数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 给药途径实体
export interface MedicationRoutes {
	// 主键Id
	id: number;
	// 途径编码
	routeCode: string;
	// 途径名称
	routeName?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 缩写
	abbreviation: string;
	// 分类
	routeCategory: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}