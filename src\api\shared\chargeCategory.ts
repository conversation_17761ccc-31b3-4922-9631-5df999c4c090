﻿import {useBaseApi} from '/@/api/base';

// 收费类别接口服务
export const useChargeCategoryApi = () => {
	const baseApi = useBaseApi("chargeCategory");
	return {
		// 分页查询收费类别
		page: baseApi.page,
		// 查看收费类别详细
		detail: baseApi.detail,
		// 新增收费类别
		add: baseApi.add,
		// 更新收费类别
		update: baseApi.update,
		// 设置收费类别状态
		setStatus: baseApi.setStatus,
		// 删除收费类别
		delete: baseApi.delete,
		// 批量删除收费类别
		batchDelete: baseApi.batchDelete,
		// 导出收费类别数据
		exportData: baseApi.exportData,
		// 导入收费类别数据
		importData: baseApi.importData,
		// 下载收费类别数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 收费类别实体
export interface ChargeCategory {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 提成
	commission: number;
	// 记账属性
	accountAttribute?: string;
	// 类型
	type?: string;
	// 医保类型
	medInsType?: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}