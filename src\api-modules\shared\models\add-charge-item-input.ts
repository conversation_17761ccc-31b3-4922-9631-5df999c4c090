/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目增加输入参数
 *
 * @export
 * @interface AddChargeItemInput
 */
export interface AddChargeItemInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    name: string;

    /**
     * 单位
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    unit?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    spec?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    price: number;

    /**
     * 进价
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    purchasePrice?: number | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    model?: string | null;

    /**
     * 批件产品名称
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    approvalName?: string | null;

    /**
     * 产地
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    producer?: string | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    manufacturer?: string | null;

    /**
     * 注册证号
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    registrationNumber?: string | null;

    /**
     * 物价编码
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    priceCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    chargeCategoryId: number;

    /**
     * 核算类别
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    calculateCategoryId: number;

    /**
     * 电子发票费用类别
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    dzfpChargeCategory?: string | null;

    /**
     * 病案首页费用类别
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    basyChargeCategory?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof AddChargeItemInput
     */
    highValue?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof AddChargeItemInput
     */
    useSeparately: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof AddChargeItemInput
     */
    uploadDw: YesNoEnum;

    /**
     * 频次
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    frequencyId?: number | null;

    /**
     * 样本类型
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    sampleType?: string | null;

    /**
     * 护理等级
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    nurseLevel?: string | null;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    checkCategoryId?: number | null;

    /**
     * 退费模式
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    refundMode?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof AddChargeItemInput
     */
    _package?: YesNoEnum;

    /**
     * 使用科室
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    useDepts?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof AddChargeItemInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddChargeItemInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddChargeItemInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    orderNo?: number | null;

    /**
     * 检查部位
     *
     * @type {number}
     * @memberof AddChargeItemInput
     */
    checkPointId?: number | null;
}
