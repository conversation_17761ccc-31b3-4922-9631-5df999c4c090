/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListChargeCategoryOutput } from './sql-sugar-paged-list-charge-category-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListChargeCategoryOutput
 */
export interface AdminResultSqlSugarPagedListChargeCategoryOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListChargeCategoryOutput}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    result?: SqlSugarPagedListChargeCategoryOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListChargeCategoryOutput
     */
    time?: Date;
}
