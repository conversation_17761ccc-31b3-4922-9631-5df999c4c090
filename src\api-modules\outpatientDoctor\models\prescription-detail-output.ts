/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方明细表输出参数
 *
 * @export
 * @interface PrescriptionDetailOutput
 */
export interface PrescriptionDetailOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    id?: number;

    /**
     * 处方主表Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    prescriptionId?: number | null;

    /**
     * 药品Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    drugId?: number | null;

    /**
     * 药品编码
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    drugCode?: string | null;

    /**
     * 药品名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    drugName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    quantity?: number | null;

    /**
     * 单次量
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    singleDose?: number | null;

    /**
     * 单次量单位
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    singleDoseUnit?: string | null;

    /**
     * 给药途径Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    medicationRoutesId?: number | null;

    /**
     * 给药途径名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    medicationRoutesName?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    frequencyName?: string | null;

    /**
     * 用药天数
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    medicationDays?: number | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    amount?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    manufacturer?: string | null;

    /**
     * 药房Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    pharmacyId?: number | null;

    /**
     * 药房名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    pharmacyName?: string | null;

    /**
     * 组标志
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    groupFlag?: string | null;

    /**
     * 组号
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    groupNo?: string | null;

    /**
     * 药品限制标志
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    drugLimitFlag?: number | null;

    /**
     * 药品待发标志
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    drugPendingFlag?: number | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    chargeCategoryId?: number | null;

    /**
     * 剂量单位
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    dosageUnit?: string | null;

    /**
     * 剂量值
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    dosageValue?: number | null;

    /**
     * 含量
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    contentValue?: number | null;

    /**
     * 含量单位
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    contentUnit?: string | null;

    /**
     * 门诊包装数量
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    outpatientPackageQuantity?: number | null;

    /**
     * 最小包装单位
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    minPackageUnit?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof PrescriptionDetailOutput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof PrescriptionDetailOutput
     */
    refundTime?: Date | null;

    /**
     * 库存零售价
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    inventorySalePrice?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof PrescriptionDetailOutput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 用药方式 1治疗用药 2预防用药
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    medicationMethod?: number | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    medicineCode?: string | null;

    /**
     * 用法Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    usageId?: number | null;

    /**
     * 用法编码
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    usageCode?: string | null;

    /**
     * 用法名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    usageName?: string | null;

    /**
     * 是否皮试
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    isSkinTest?: number | null;

    /**
     * 皮试结果
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    skinTestResults?: number | null;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    createOrgName?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionDetailOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionDetailOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionDetailOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionDetailOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionDetailOutput
     */
    remark?: string | null;
}
