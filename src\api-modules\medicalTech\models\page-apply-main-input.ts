/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 申请单分页查询输入参数
 *
 * @export
 * @interface PageApplyMainInput
 */
export interface PageApplyMainInput {

    /**
     * @type {Search}
     * @memberof PageApplyMainInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageApplyMainInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageApplyMainInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageApplyMainInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    descStr?: string | null;

    /**
     * 申请单号
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    applyNo?: string | null;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof PageApplyMainInput
     */
    registerId?: number | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof PageApplyMainInput
     */
    prescriptionType?: string | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof PageApplyMainInput
     */
    flag?: number | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageApplyMainInput
     */
    selectKeyList?: Array<number> | null;
}
