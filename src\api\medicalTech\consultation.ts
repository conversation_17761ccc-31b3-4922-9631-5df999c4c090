﻿import { useBaseApi } from '/@/api/base';

// 会诊表接口服务
export const useConsultationApi = () => {
	const baseApi = useBaseApi('consultation');
	return {
		// 分页查询会诊表
		page: baseApi.page,
		// 查看会诊表详细
		detail: baseApi.detail,
		// 新增会诊表
		add: baseApi.add,
		// 更新会诊表
		update: baseApi.update,
		// 删除会诊表
		delete: baseApi.delete,
		// 批量删除会诊表
		batchDelete: baseApi.batchDelete,
	};
};

// 会诊表实体
export interface Consultation {
	// 主键Id
	id: number;
	// 申请单号
	applyNo: string;
	// 就诊Id
	registerId?: number;
	// 就诊流水号
	visitNo?: string | null;
	//门诊号
	outpatientNo?: string | null;
	//就诊卡号
	cardNo?: string | null;
	// 患者Id
	patientId?: number | null;
	// 患者姓名
	patientName?: string | null;
	// 就诊时间
	visitTime: string | null;
	// 性别
	sex?: string | null;
	// 年龄
	age?: number | null;
	// 年龄单位
	ageUnit?: string | null;
	// 门诊住院标识 0门诊 1住院
	flag: number;
	// 期望会诊时间
	expectedTime: string;
	// 会诊类型
	type?: string;
	// 会诊状态
	status: string;
	// 病情摘要
	clinicalSummary?: string;
	// 会诊目的
	purpose?: string;
	// 会诊意见
	consultationOpinion: string;
	// 意见填写时间
	opinionTime: string;
	// 意见填写人Id
	opinionStaffId: number;
	// 意见填写人名称
	opinionStaffName: string;
	// 意见填写人签名
	opinionStaffSign: string;
	// 申请时间
	applyTime: string;
	// 申请科室Id
	applyDeptId: number;
	// 申请科室名称
	applyDeptName: string;
	// 申请医生Id
	applyDoctorId: number;
	// 申请医生名称
	applyDoctorName: string;
	// 申请医生签名
	applyDoctorSign: string;
	// 会诊科室Id
	consultationDeptId: number;
	// 会诊科室名称
	consultationDeptName: string;
	// 会诊医生Id
	consultationDoctorId: number;
	// 会诊医生名称
	consultationDoctorName: string;
	// 会诊接受时间
	consultationAcceptTime: string;
	// 会诊结束时间
	consultationEndTime: string;
	// 院外会诊机构名称
	outsideHospitalName: string;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	//收费项目Id
	itemId: number;
}
