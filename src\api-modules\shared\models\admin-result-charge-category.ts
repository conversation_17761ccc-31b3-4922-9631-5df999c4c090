/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeCategory } from './charge-category';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultChargeCategory
 */
export interface AdminResultChargeCategory {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultChargeCategory
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultChargeCategory
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultChargeCategory
     */
    message?: string | null;

    /**
     * @type {ChargeCategory}
     * @memberof AdminResultChargeCategory
     */
    result?: ChargeCategory;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultChargeCategory
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultChargeCategory
     */
    time?: Date;
}
