﻿import { useBaseApi } from '/@/api/base';

// 出库管理接口服务
export const useStorageOutRecordApi = () => {
	const baseApi = useBaseApi('storageOutRecord');
	return {
		// 分页查询出库管理
		page: baseApi.page,
		// 查看出库管理详细
		detail: baseApi.detail,
		// 新增出库管理
		add: baseApi.add,
		// 更新出库管理
		update: baseApi.update,
		// 删除出库管理
		delete: baseApi.delete,
		// 批量删除出库管理
		batchDelete: baseApi.batchDelete,
		// 导出出库管理数据
		exportData: baseApi.exportData,
		// 导入出库管理数据
		importData: baseApi.importData,
		// 下载出库管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, targetDeptFilter: string = '', cancel: boolean = false) => baseApi.dropdownData({ targetDeptFilter, fromPage }, cancel),
		//提交
		submit: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'submit',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//审核通过
		Approve: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'approve',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//审核拒绝
		Reject: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'reject',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};

// 出库管理实体
export interface StorageOutRecord {
	// 主键Id
	id: number;
	// 库房
	storageId: number;
	// 库房编码
	storageCode: string;
	// 药品类型
	drugType: string;
	// 出库单号
	storageOutNo: string;
	// 供应商ID
	supplierId: number;
	// 供应商编码
	supplierCode: string;
	// 供应商名称
	supplierName: string;
	// 出库类型
	storageOutType: string;
	// 出库日期
	storageOutTime: string;
	// 目标科室
	targetDeptId: number;
	// 目标科室编码
	targetDeptCode: string;
	// 目标科室名称
	targetDeptName: string;
	// 领用人ID
	targetUserId: number;
	// 领用人编码
	targetUserCode: string;
	// 领用人名称
	targetUserName: string;
	// 总进价
	totalPurchasePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 发票号
	invoiceNo: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}
