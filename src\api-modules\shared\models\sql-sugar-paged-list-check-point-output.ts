/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CheckPointOutput } from './check-point-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListCheckPointOutput
 */
export interface SqlSugarPagedListCheckPointOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<CheckPointOutput>}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    items?: Array<CheckPointOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCheckPointOutput
     */
    hasNextPage?: boolean;
}
