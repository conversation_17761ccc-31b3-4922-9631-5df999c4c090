/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PrescriptionMainOutput } from './prescription-main-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListPrescriptionMainOutput
 */
export interface SqlSugarPagedListPrescriptionMainOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<PrescriptionMainOutput>}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    items?: Array<PrescriptionMainOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionMainOutput
     */
    hasNextPage?: boolean;
}
