/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 
 *
 * @export
 * @interface PageIcd10Input
 */
export interface PageIcd10Input {

    /**
     * @type {Search}
     * @memberof PageIcd10Input
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageIcd10Input
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageIcd10Input
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageIcd10Input
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    descStr?: string | null;

    /**
     * 父节点Id
     *
     * @type {number}
     * @memberof PageIcd10Input
     */
    pid?: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    name?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageIcd10Input
     */
    code?: string | null;
}
