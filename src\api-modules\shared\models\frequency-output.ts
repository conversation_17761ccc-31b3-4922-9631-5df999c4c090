/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 频次输出参数
 *
 * @export
 * @interface FrequencyOutput
 */
export interface FrequencyOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    wubiCode?: string | null;

    /**
     * 时间间隔
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    timeInterval?: number | null;

    /**
     * 时间单位
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    timeUnit?: number | null;

    /**
     * 执行频率
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    executionFrequency?: number | null;

    /**
     * 执行时间
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    executionTime?: string | null;

    /**
     * 持续标识
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    sustain?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof FrequencyOutput
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * @type {StatusEnum}
     * @memberof FrequencyOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof FrequencyOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof FrequencyOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof FrequencyOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof FrequencyOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof FrequencyOutput
     */
    remark?: string | null;
}
