/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListChargeItemOutput } from './sql-sugar-paged-list-charge-item-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListChargeItemOutput
 */
export interface AdminResultSqlSugarPagedListChargeItemOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListChargeItemOutput}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    result?: SqlSugarPagedListChargeItemOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListChargeItemOutput
     */
    time?: Date;
}
