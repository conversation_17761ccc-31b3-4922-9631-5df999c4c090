/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddIcd10Input } from '../models';
import { AdminResultListIcd10 } from '../models';
import { AdminResultSqlSugarPagedListIcd10 } from '../models';
import { DeleteIcd10Input } from '../models';
import { PageIcd10Input } from '../models';
import { SetStatusIcd10Input } from '../models';
import { UpdateIcd10Input } from '../models';
/**
 * Icd10Api - axios parameter creator
 * @export
 */
export const Icd10ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加疾病
         * @param {AddIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10AddPost: async (body?: AddIcd10Input, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除疾病
         * @param {DeleteIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10DeletePost: async (body?: DeleteIcd10Input, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取疾病列表
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10ListIdGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiIcd10ListIdGet.');
            }
            const localVarPath = `/api/icd10/list/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取疾病分页列表
         * @param {PageIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10PagePost: async (body?: PageIcd10Input, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10SetStatusPost: async (body?: SetStatusIcd10Input, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10SyncPost: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/sync`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新疾病
         * @param {UpdateIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIcd10UpdatePost: async (body?: UpdateIcd10Input, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/icd10/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * Icd10Api - functional programming interface
 * @export
 */
export const Icd10ApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加疾病
         * @param {AddIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10AddPost(body?: AddIcd10Input, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10AddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除疾病
         * @param {DeleteIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10DeletePost(body?: DeleteIcd10Input, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10DeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取疾病列表
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10ListIdGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListIcd10>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10ListIdGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取疾病分页列表
         * @param {PageIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10PagePost(body?: PageIcd10Input, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListIcd10>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10PagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10SetStatusPost(body?: SetStatusIcd10Input, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10SetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10SyncPost(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10SyncPost(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新疾病
         * @param {UpdateIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10UpdatePost(body?: UpdateIcd10Input, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await Icd10ApiAxiosParamCreator(configuration).apiIcd10UpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * Icd10Api - factory interface
 * @export
 */
export const Icd10ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加疾病
         * @param {AddIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10AddPost(body?: AddIcd10Input, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return Icd10ApiFp(configuration).apiIcd10AddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除疾病
         * @param {DeleteIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10DeletePost(body?: DeleteIcd10Input, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return Icd10ApiFp(configuration).apiIcd10DeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取疾病列表
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10ListIdGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListIcd10>> {
            return Icd10ApiFp(configuration).apiIcd10ListIdGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取疾病分页列表
         * @param {PageIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10PagePost(body?: PageIcd10Input, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListIcd10>> {
            return Icd10ApiFp(configuration).apiIcd10PagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10SetStatusPost(body?: SetStatusIcd10Input, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return Icd10ApiFp(configuration).apiIcd10SetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10SyncPost(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return Icd10ApiFp(configuration).apiIcd10SyncPost(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新疾病
         * @param {UpdateIcd10Input} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIcd10UpdatePost(body?: UpdateIcd10Input, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return Icd10ApiFp(configuration).apiIcd10UpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Icd10Api - object-oriented interface
 * @export
 * @class Icd10Api
 * @extends {BaseAPI}
 */
export class Icd10Api extends BaseAPI {
    /**
     * 
     * @summary 增加疾病
     * @param {AddIcd10Input} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10AddPost(body?: AddIcd10Input, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return Icd10ApiFp(this.configuration).apiIcd10AddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除疾病
     * @param {DeleteIcd10Input} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10DeletePost(body?: DeleteIcd10Input, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return Icd10ApiFp(this.configuration).apiIcd10DeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取疾病列表
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10ListIdGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListIcd10>> {
        return Icd10ApiFp(this.configuration).apiIcd10ListIdGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取疾病分页列表
     * @param {PageIcd10Input} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10PagePost(body?: PageIcd10Input, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListIcd10>> {
        return Icd10ApiFp(this.configuration).apiIcd10PagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置状态
     * @param {SetStatusIcd10Input} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10SetStatusPost(body?: SetStatusIcd10Input, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return Icd10ApiFp(this.configuration).apiIcd10SetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10SyncPost(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return Icd10ApiFp(this.configuration).apiIcd10SyncPost(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新疾病
     * @param {UpdateIcd10Input} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Icd10Api
     */
    public async apiIcd10UpdatePost(body?: UpdateIcd10Input, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return Icd10ApiFp(this.configuration).apiIcd10UpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
