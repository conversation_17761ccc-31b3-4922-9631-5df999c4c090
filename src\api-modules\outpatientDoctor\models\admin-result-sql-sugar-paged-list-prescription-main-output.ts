/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListPrescriptionMainOutput } from './sql-sugar-paged-list-prescription-main-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListPrescriptionMainOutput
 */
export interface AdminResultSqlSugarPagedListPrescriptionMainOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListPrescriptionMainOutput}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    result?: SqlSugarPagedListPrescriptionMainOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListPrescriptionMainOutput
     */
    time?: Date;
}
