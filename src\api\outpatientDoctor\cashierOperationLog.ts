import { useBaseApi } from '/@/api/base';

/**
 * 收款员操作日志接口服务
 */
export const useCashierOperationLogApi = () => {
	const baseApi = useBaseApi('CashierOperationLog');
	return {
		/**
		 * 记录收费操作日志
		 */
		logChargeOperation: function (data: {
			operationType: number;
			operationDesc: string;
			businessId: number;
			beforeData?: any;
			afterData?: any;
			operationReason?: string;
			operationResult?: number;
			errorMessage?: string;
			operationDuration?: number;
		}, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'logChargeOperation',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 记录充值操作日志
		 */
		logRechargeOperation: function (data: {
			operationType: number;
			operationDesc: string;
			businessId: number;
			beforeData?: any;
			afterData?: any;
			operationReason?: string;
			operationResult?: number;
			errorMessage?: string;
			operationDuration?: number;
		}, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'logRechargeOperation',
					method: 'post',
					data,
				},
				cancel
			);
		},

		/**
		 * 获取操作日志
		 */
		getOperationLogs: function (settlementId: number, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getOperationLogs',
					method: 'post',
					data: { settlementId },
				},
				cancel
			);
		},

		/**
		 * 获取收款员操作日志
		 */
		getCashierOperationLogs: function (data: {
			cashierId: number;
			startDate: string;
			endDate: string;
			operationType?: number;
		}, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getCashierOperationLogs',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};
