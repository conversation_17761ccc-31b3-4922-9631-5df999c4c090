/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 给药途径分页查询输入参数
 *
 * @export
 * @interface PageMedicationRoutesInput
 */
export interface PageMedicationRoutesInput {

    /**
     * @type {Search}
     * @memberof PageMedicationRoutesInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageMedicationRoutesInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageMedicationRoutesInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageMedicationRoutesInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    descStr?: string | null;

    /**
     * 途径编码
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    routeCode?: string | null;

    /**
     * 途径名称
     *
     * @type {string}
     * @memberof PageMedicationRoutesInput
     */
    routeName?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageMedicationRoutesInput
     */
    selectKeyList?: Array<number> | null;
}
