/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SchedulingPlanOutput } from './scheduling-plan-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListSchedulingPlanOutput
 */
export interface SqlSugarPagedListSchedulingPlanOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<SchedulingPlanOutput>}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    items?: Array<SchedulingPlanOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListSchedulingPlanOutput
     */
    hasNextPage?: boolean;
}
