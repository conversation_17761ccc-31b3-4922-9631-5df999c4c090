/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查类别输出参数
 *
 * @export
 * @interface CheckCategoryOutput
 */
export interface CheckCategoryOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    wubiCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    chargeCategoryId?: number | null;

    /**
     * 收费类别 描述
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    chargeCategoryFkDisplayName?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof CheckCategoryOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CheckCategoryOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CheckCategoryOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CheckCategoryOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CheckCategoryOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof CheckCategoryOutput
     */
    remark?: string | null;
}
