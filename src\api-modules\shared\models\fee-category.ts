/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedCategoryEnum } from './med-category-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 费用类别表
 *
 * @export
 * @interface FeeCategory
 */
export interface FeeCategory {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof FeeCategory
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof FeeCategory
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof FeeCategory
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof FeeCategory
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof FeeCategory
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof FeeCategory
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof FeeCategory
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof FeeCategory
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof FeeCategory
     */
    tenantId?: number | null;

    /**
     * 编号
     *
     * @type {string}
     * @memberof FeeCategory
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof FeeCategory
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof FeeCategory
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof FeeCategory
     */
    wubiCode?: string | null;

    /**
     * @type {MedCategoryEnum}
     * @memberof FeeCategory
     */
    medCategory?: MedCategoryEnum;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof FeeCategory
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * 医保id
     *
     * @type {number}
     * @memberof FeeCategory
     */
    medInsId?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof FeeCategory
     */
    medInsType?: MedInsTypeEnum;

    /**
     * @type {StatusEnum}
     * @memberof FeeCategory
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof FeeCategory
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof FeeCategory
     */
    remark?: string | null;
}
