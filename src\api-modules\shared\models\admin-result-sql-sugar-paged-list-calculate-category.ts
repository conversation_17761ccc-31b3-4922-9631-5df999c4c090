/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListCalculateCategory } from './sql-sugar-paged-list-calculate-category';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListCalculateCategory
 */
export interface AdminResultSqlSugarPagedListCalculateCategory {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListCalculateCategory}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    result?: SqlSugarPagedListCalculateCategory;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListCalculateCategory
     */
    time?: Date;
}
