/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 频次表
 *
 * @export
 * @interface Frequency
 */
export interface Frequency {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof Frequency
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof Frequency
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof Frequency
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof Frequency
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof Frequency
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof Frequency
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof Frequency
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof Frequency
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof Frequency
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof Frequency
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof Frequency
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof Frequency
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof Frequency
     */
    wubiCode?: string | null;

    /**
     * 时间间隔
     *
     * @type {number}
     * @memberof Frequency
     */
    timeInterval?: number | null;

    /**
     * 时间单位 0时 1天 2周
     *
     * @type {number}
     * @memberof Frequency
     */
    timeUnit?: number | null;

    /**
     * 执行频率
     *
     * @type {number}
     * @memberof Frequency
     */
    executionFrequency?: number | null;

    /**
     * 执行时间
     *
     * @type {string}
     * @memberof Frequency
     */
    executionTime?: string | null;

    /**
     * 持续标识 0持续 1非持续
     *
     * @type {number}
     * @memberof Frequency
     */
    sustain?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof Frequency
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * @type {StatusEnum}
     * @memberof Frequency
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof Frequency
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof Frequency
     */
    remark?: string | null;
}
