/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 设置核算类别状态输入参数
 *
 * @export
 * @interface SetStatusCalculateCategoryInput
 */
export interface SetStatusCalculateCategoryInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof SetStatusCalculateCategoryInput
     */
    id: number;

    /**
     * @type {StatusEnum}
     * @memberof SetStatusCalculateCategoryInput
     */
    status?: StatusEnum;
}
