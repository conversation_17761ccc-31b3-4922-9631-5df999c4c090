﻿import {useBaseApi} from '/@/api/base';

// 复诊预约记录接口服务
export const usePatientRevisitAppointmentApi = () => {
	const baseApi = useBaseApi("patientRevisitAppointment");
	return {
		// 分页查询复诊预约记录
		page: baseApi.page,
		// 查看复诊预约记录详细
		detail: baseApi.detail,
		// 新增复诊预约记录
		add: baseApi.add,
		// 更新复诊预约记录
		update: baseApi.update,
		// 删除复诊预约记录
		delete: baseApi.delete,
		// 批量删除复诊预约记录
		batchDelete: baseApi.batchDelete,
		// 导出复诊预约记录数据
		exportData: baseApi.exportData,
		// 导入复诊预约记录数据
		importData: baseApi.importData,
		// 下载复诊预约记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 复诊预约记录实体
export interface PatientRevisitAppointment {
	// 主键Id
	id: number;
	// 患者Id
	patientId?: number;
	// 患者姓名
	patientName: string;
	// 挂号记录Id
	registerId: number;
	// 就诊卡号
	visitNo: string;
	// 身份证号
	idCardNo: string;
	// 门诊号
	outpatientNo: string;
	// 预约医生Id
	appointmentDoctorId: number;
	// 预约医生姓名
	appointmentDoctorName: string;
	// 预约科室Id
	appointmentDeptId: number;
	// 预约科室名称
	appointmentDeptName: string;
	// 复诊时间
	revisitTime: string;
	// 复诊原因
	revisitReason: string;
	// 复诊科室Id
	revisitDeptId: number;
	// 复诊科室名称
	revisitDeptName: string;
	// 状态，默认1（有效）
	status: number;
	// 备注信息
	remark: string;
	// 创建机构Id
	createOrgId: number;
	// 创建机构名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}