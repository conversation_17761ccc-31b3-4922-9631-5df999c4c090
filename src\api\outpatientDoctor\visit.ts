﻿import { useBaseApi } from '/@/api/base';

// 门诊挂号接口服务
export const useVisitApi = () => {
	const baseApi = useBaseApi('outpatientVisit');
	return {
		
	 
		getVisit: function (visitNo:any , visitId :any=0,cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getVisit'+ '?visitId=' + visitId + '&visitNo=' + visitNo,
					method: 'get'
					
				},
				cancel
			);
		},
		getList: function (data :any,cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getList' ,
					method: 'post',
					data,
				},
				cancel
			);
		}
	};
};
