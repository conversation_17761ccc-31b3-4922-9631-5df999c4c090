/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { BusinessTypeEnum } from './business-type-enum';
import { CardStatusEnum } from './card-status-enum';
 /**
 * 
 *
 * @export
 * @interface CardInfoDto
 */
export interface CardInfoDto {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof CardInfoDto
     */
    id?: number;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof CardInfoDto
     */
    cardNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof CardInfoDto
     */
    patientId?: number;

    /**
     * @type {BusinessTypeEnum}
     * @memberof CardInfoDto
     */
    businessType?: BusinessTypeEnum;

    /**
     * 密码
     *
     * @type {string}
     * @memberof CardInfoDto
     */
    password?: string | null;

    /**
     * 余额
     *
     * @type {number}
     * @memberof CardInfoDto
     */
    balance?: number | null;

    /**
     * @type {CardStatusEnum}
     * @memberof CardInfoDto
     */
    status?: CardStatusEnum;

    /**
     * 是否储值
     *
     * @type {boolean}
     * @memberof CardInfoDto
     */
    isStored?: boolean | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof CardInfoDto
     */
    remark?: string | null;

    /**
     * 指定使用科室，多个以“,”分割
     *
     * @type {string}
     * @memberof CardInfoDto
     */
    useDepts?: string | null;

    /**
     * 指定充值方式，多个以“,”分割
     *
     * @type {string}
     * @memberof CardInfoDto
     */
    chargeModes?: string | null;
}
