/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListRegisterOutput } from './sql-sugar-paged-list-register-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListRegisterOutput
 */
export interface AdminResultSqlSugarPagedListRegisterOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListRegisterOutput}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    result?: SqlSugarPagedListRegisterOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListRegisterOutput
     */
    time?: Date;
}
