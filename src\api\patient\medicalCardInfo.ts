import { useBaseApi } from '/@/api/base';

// 就诊卡信息接口服务
export const useMedicalCardInfoApi = () => {
	const baseApi = useBaseApi('medicalCardInfo');
	return {
		...baseApi, // 使用预设方法：page, detail, add, update等

		// 根据身份证号或者卡号获取就诊卡信息
		cardInfoByIdOrCardNo: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'cardInfoByIdOrCardNo',
					method: 'post',
					data,
				},
				cancel
			);
		},

		// 挂失
		loss: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'loss',
					method: 'post',
					data,
				},
				cancel
			);
		},

		// 恢复
		restore: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'restore',
					method: 'post',
					data,
				},
				cancel
			);
		},

		// 退卡
		cardRefund: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'cardRefund',
					method: 'post',
					data,
				},
				cancel
			);
		},

		// 卡充值
		cardRecharge: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'cardRecharge',
					method: 'post',
					data,
				},
				cancel
			);
		},

		// 卡缴费
		cardPay: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'cardPay',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};
