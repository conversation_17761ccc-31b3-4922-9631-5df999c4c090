/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedCategoryEnum } from './med-category-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 费用类别增加输入参数
 *
 * @export
 * @interface AddFeeCategoryInput
 */
export interface AddFeeCategoryInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddFeeCategoryInput
     */
    name: string;

    /**
     * @type {MedCategoryEnum}
     * @memberof AddFeeCategoryInput
     */
    medCategory: MedCategoryEnum;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof AddFeeCategoryInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * 医保id
     *
     * @type {number}
     * @memberof AddFeeCategoryInput
     */
    medInsId?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof AddFeeCategoryInput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * @type {StatusEnum}
     * @memberof AddFeeCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddFeeCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddFeeCategoryInput
     */
    remark?: string | null;
}
