﻿import {useBaseApi} from '/@/api/base';

// 挂号类别接口服务
export const useRegCategoryApi = () => {
	const baseApi = useBaseApi("regCategory");
	return {
		// 分页查询挂号类别
		page: baseApi.page,
		// 查看挂号类别详细
		detail: baseApi.detail,
		// 新增挂号类别
		add: baseApi.add,
		// 更新挂号类别
		update: baseApi.update,
		// 设置挂号类别状态
		setStatus: baseApi.setStatus,
		// 删除挂号类别
		delete: baseApi.delete,
		// 批量删除挂号类别
		batchDelete: baseApi.batchDelete,
		// 导出挂号类别数据
		exportData: baseApi.exportData,
		// 导入挂号类别数据
		importData: baseApi.importData,
		// 下载挂号类别数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 挂号类别实体
export interface RegCategory {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 挂号费
	registrationFee: number;
	// 诊疗费
	consultationFee: number;
	// 收费项目
	chargeItemId: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}