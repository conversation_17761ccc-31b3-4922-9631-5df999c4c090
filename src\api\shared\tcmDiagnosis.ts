﻿import {useBaseApi} from '/@/api/base';

// 中医诊断接口服务
export const useTcmDiagnosisApi = () => {
	const baseApi = useBaseApi("tcmDiagnosis");
	return {
		// 分页查询中医诊断
		page: baseApi.page,
		// 查看中医诊断详细
		detail: baseApi.detail,
		// 新增中医诊断
		add: baseApi.add,
		// 更新中医诊断
		update: baseApi.update,
		// 设置中医诊断状态
		setStatus: baseApi.setStatus,
		// 删除中医诊断
		delete: baseApi.delete,
		// 批量删除中医诊断
		batchDelete: baseApi.batchDelete,
		// 导出中医诊断数据
		exportData: baseApi.exportData,
		// 导入中医诊断数据
		importData: baseApi.importData,
		// 下载中医诊断数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 中医诊断实体
export interface TcmDiagnosis {
	// 主键Id
	id: number;
	// 中医诊断编码
	tcmDiagnosisCode?: string;
	// 中医诊断名称
	tcmDiagnosisName?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 版本
	version: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 租户Id
	tenantId: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}