/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListChargeItemPackOutput } from './sql-sugar-paged-list-charge-item-pack-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListChargeItemPackOutput
 */
export interface AdminResultSqlSugarPagedListChargeItemPackOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListChargeItemPackOutput}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    result?: SqlSugarPagedListChargeItemPackOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListChargeItemPackOutput
     */
    time?: Date;
}
