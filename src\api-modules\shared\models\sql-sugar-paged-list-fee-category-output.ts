/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { FeeCategoryOutput } from './fee-category-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListFeeCategoryOutput
 */
export interface SqlSugarPagedListFeeCategoryOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<FeeCategoryOutput>}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    items?: Array<FeeCategoryOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListFeeCategoryOutput
     */
    hasNextPage?: boolean;
}
