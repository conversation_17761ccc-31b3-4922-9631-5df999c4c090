/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PrescriptionTypeOutput } from './prescription-type-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListPrescriptionTypeOutput
 */
export interface SqlSugarPagedListPrescriptionTypeOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<PrescriptionTypeOutput>}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    items?: Array<PrescriptionTypeOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPrescriptionTypeOutput
     */
    hasNextPage?: boolean;
}
