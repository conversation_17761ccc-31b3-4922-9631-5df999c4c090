﻿import {useBaseApi} from '/@/api/base';

// 门诊收费接口服务
export const useChargeApi = () => {
	const baseApi = useBaseApi("Charge");
	return {
 
		// 查询已计费数据用于退费申请
        ListOfRefund: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "ListOfRefund",
                method: 'post',
                data,
            }, cancel);
        },
        getDetail: function (id: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getDetail",
                method: 'get',
                data: { id },
            }, cancel);
        },
        GetChargeList: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "GetChargeList",
                method: 'post',
                data ,
            }, cancel);
        },
	}
}
 