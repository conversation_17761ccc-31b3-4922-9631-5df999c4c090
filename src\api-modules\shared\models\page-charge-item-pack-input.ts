/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 单项套餐分页查询输入参数
 *
 * @export
 * @interface PageChargeItemPackInput
 */
export interface PageChargeItemPackInput {

    /**
     * @type {Search}
     * @memberof PageChargeItemPackInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageChargeItemPackInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageChargeItemPackInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageChargeItemPackInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageChargeItemPackInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageChargeItemPackInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageChargeItemPackInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageChargeItemPackInput
     */
    descStr?: string | null;

    /**
     * 套餐Id
     *
     * @type {number}
     * @memberof PageChargeItemPackInput
     */
    packId?: number | null;
}
