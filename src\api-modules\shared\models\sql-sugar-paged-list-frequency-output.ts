/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { FrequencyOutput } from './frequency-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListFrequencyOutput
 */
export interface SqlSugarPagedListFrequencyOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<FrequencyOutput>}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    items?: Array<FrequencyOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListFrequencyOutput
     */
    hasNextPage?: boolean;
}
