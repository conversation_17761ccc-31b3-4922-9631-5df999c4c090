/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PayMethod } from './pay-method';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListPayMethod
 */
export interface SqlSugarPagedListPayMethod {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListPayMethod
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListPayMethod
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPayMethod
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListPayMethod
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<PayMethod>}
     * @memberof SqlSugarPagedListPayMethod
     */
    items?: Array<PayMethod> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPayMethod
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListPayMethod
     */
    hasNextPage?: boolean;
}
