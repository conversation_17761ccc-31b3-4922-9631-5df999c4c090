<script lang="ts" setup name="appointment-record">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';

import { useAppointmentRecordApi } from '/@/api/inpatient/appointmentRecord';

import { useFeeCategoryApi } from '/@/api/shared/feeCategory'; //费用类别
import editDialog from '/@/views/inpatient/appointment/record/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';

import { useRouter } from 'vue-router';
import { deepClone } from '/@/utils/other';
const router = useRouter(); // 路由对象
const appointmentRecordApi = useAppointmentRecordApi();
const feeCategoryApi = useFeeCategoryApi();
const printDialogRef = ref();

const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	appointmentStatus: [] as any[],
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
});

// 页面加载时
onMounted(async () => {
	console.log('appointmentRecord mounted');
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	console.log('handleQuery');
	state.tableLoading = true;
	state.dropdownData.feeCategoryData = await feeCategoryApi.page({ pageSize: 100, status: 1, usageScope: 2 }).then((res) => res.data.result?.items ?? []);

	// .then(res => {
	//   state.dropdownData.feeCategoryData = res.data.result.items;
	// })
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await appointmentRecordApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];
	state.tableLoading = false;
};
const getFeeCategoryName = (feeId: number) => {
	const feeCategory = state.dropdownData.feeCategoryData.find((item: any) => item.id === feeId);
	return feeCategory ? feeCategory.name : '';
};
// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

const confirm = async (row: any) => {
	let obj = deepClone(row);
	obj.id = 0;
	obj.appointmentRecordId = row.id;
	// obj.formSource = '1'//门诊预约
	router.push({
		path: '/inpatientregister/register/create',
		state: obj, //{ "appointmentRecordId": row.id }
	});
	// state.tableLoading = true;
	// let values = {};
	// values = {
	//   id: row.id,
	//   status: 1
	// }
	// await appointmentRecordApi.confirm(values).then(res => {
	//   ElMessage.success('操作成功')
	//   state.tableLoading = false;
	//   handleQuery();
	// }).catch(err => {
	//   ElMessage({
	//     message: `操作失败`,
	//     type: "error",
	//   });
	//   state.tableLoading = false;
	// });
};

handleQuery();
</script>
<template>
	<div class="appointment-record-container" v-loading="state.exportLoading">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="患者姓名">
							<el-input v-model="state.tableQueryParams.patientName" clearable placeholder="请输入患者姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="预约时间">
							<el-date-picker
								type="daterange"
								v-model="state.tableQueryParams.appointmentTimeRange"
								value-format="YYYY-MM-DD HH:mm:ss"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
							/>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="证件号码">
							<el-input v-model="state.tableQueryParams.idCardNo" clearable placeholder="请输入证件号码" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="预约科室">
							<el-input v-model="state.tableQueryParams.deptName" clearable placeholder="请输入预约科室" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'appointmentRecord:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('appointmentRecord:batchDelete') || auth('appointmentRecord:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="patientNo" label="患者唯一号" show-overflow-tooltip />
				<el-table-column prop="outpatientNo" label="门诊号" show-overflow-tooltip />

				<el-table-column prop="medicalCardNo" label="就诊卡号" show-overflow-tooltip />
				<el-table-column prop="patientName" label="患者姓名" show-overflow-tooltip />
				<el-table-column prop="appointmentTime" label="预约时间" show-overflow-tooltip width="130" />
				<el-table-column prop="feeId" label="收费类别" width="100">
					<template #default="scope">
						<el-tag>{{ getFeeCategoryName(scope.row.feeId) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="idCardType" label="证件类型" width="130"
					><template #default="scope">
						<g-sys-dict v-model="scope.row.idCardType" code="CardTypeEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="idCardNo" label="证件号码" show-overflow-tooltip width="150" />
				<el-table-column prop="insuranceNo" label="保险号" show-overflow-tooltip width="150" />

				<el-table-column prop="deptName" label="预约科室" show-overflow-tooltip />

				<el-table-column prop="doctorName" label="预约医生" show-overflow-tooltip />
				<el-table-column prop="diagnosticCode" label="诊断代码" show-overflow-tooltip />
				<el-table-column prop="diagnosticName" label="诊断名称" show-overflow-tooltip />
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.status === 0" type="info">未确认</el-tag>
						<el-tag v-if="scope.row.status === 1" type="success">已确认</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('appointmentRecord:update')">
					<template #default="scope">
						<el-button icon="ele-Edit" :disabled="scope.row.status === 1" size="small" text type="primary" @click="confirm(scope.row)" v-auth="'appointmentRecord:update'"> 预约确认 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>

			<printDialog ref="printDialogRef" :title="'打印住院预约'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
