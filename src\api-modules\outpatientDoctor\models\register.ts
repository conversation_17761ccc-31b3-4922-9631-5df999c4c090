/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { RegStatusEnum } from './reg-status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 挂号记录表
 *
 * @export
 * @interface Register
 */
export interface Register {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof Register
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof Register
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof Register
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof Register
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof Register
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof Register
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof Register
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof Register
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof Register
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof Register
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof Register
     */
    tenantId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof Register
     */
    visitNo?: string | null;

    /**
     * 就诊次数
     *
     * @type {number}
     * @memberof Register
     */
    visitNum?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof Register
     */
    timePeriodId?: number | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof Register
     */
    regCategoryId?: number | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof Register
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof Register
     */
    patientName?: string | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof Register
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof Register
     */
    wubiCode?: string | null;

    /**
     * @type {GenderEnum}
     * @memberof Register
     */
    sex?: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof Register
     */
    age?: number | null;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof Register
     */
    ageUnit?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof Register
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof Register
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof Register
     */
    idCardNo?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof Register
     */
    occupation?: string | null;

    /**
     * 费别id
     *
     * @type {number}
     * @memberof Register
     */
    feeId?: number | null;

    /**
     * 保险号码
     *
     * @type {string}
     * @memberof Register
     */
    insuranceNum?: string | null;

    /**
     * 保险类型
     *
     * @type {string}
     * @memberof Register
     */
    insuranceType?: string | null;

    /**
     * 合同单位id
     *
     * @type {number}
     * @memberof Register
     */
    contractUnitId?: number | null;

    /**
     * 就诊类型 初诊|复诊
     *
     * @type {number}
     * @memberof Register
     */
    visitType?: number | null;

    /**
     * 就诊科室id
     *
     * @type {number}
     * @memberof Register
     */
    deptId?: number | null;

    /**
     * 医生id
     *
     * @type {number}
     * @memberof Register
     */
    doctorId?: number | null;

    /**
     * @type {RegStatusEnum}
     * @memberof Register
     */
    status?: RegStatusEnum;

    /**
     * 症状
     *
     * @type {string}
     * @memberof Register
     */
    symptom?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof Register
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof Register
     */
    consultationFee?: number | null;

    /**
     * 其他费用
     *
     * @type {number}
     * @memberof Register
     */
    otherFee?: number | null;

    /**
     * 实收费用
     *
     * @type {number}
     * @memberof Register
     */
    actualChargeFee?: number | null;

    /**
     * 退号时间
     *
     * @type {Date}
     * @memberof Register
     */
    refundNumTime?: Date | null;

    /**
     * 退号人id
     *
     * @type {number}
     * @memberof Register
     */
    refundNumId?: number | null;

    /**
     * 就诊卡id
     *
     * @type {number}
     * @memberof Register
     */
    cardId?: number | null;

    /**
     * 收费主表id
     *
     * @type {number}
     * @memberof Register
     */
    chargeMainId?: number | null;

    /**
     * 预约流水号
     *
     * @type {number}
     * @memberof Register
     */
    appSerialNum?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof Register
     */
    remark?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof Register
     */
    nation?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof Register
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof Register
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof Register
     */
    nativePlaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof Register
     */
    residenceProvince?: number | null;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof Register
     */
    residenceCity?: number | null;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof Register
     */
    residenceCounty?: number | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof Register
     */
    contactName?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof Register
     */
    contactPhone?: string | null;

    /**
     * 医保卡余额
     *
     * @type {number}
     * @memberof Register
     */
    medInsCardBalance?: number | null;

    /**
     * 个人账户信息
     *
     * @type {string}
     * @memberof Register
     */
    personalAccountInfo?: string | null;

    /**
     * 医保个人编号
     *
     * @type {string}
     * @memberof Register
     */
    medInsPersonalNum?: string | null;

    /**
     * 医保类型
     *
     * @type {number}
     * @memberof Register
     */
    medInsType?: number | null;

    /**
     * 医保统筹区号
     *
     * @type {string}
     * @memberof Register
     */
    medInsAreaCode?: string | null;

    /**
     * 医保就诊编号
     *
     * @type {string}
     * @memberof Register
     */
    medInsRegNum?: string | null;

    /**
     * 医保支付方式 0:门诊统筹 1：个人支付
     *
     * @type {string}
     * @memberof Register
     */
    medInsPayType?: string | null;

    /**
     * 结算病种id
     *
     * @type {number}
     * @memberof Register
     */
    settleDiseaseTypeId?: number | null;

    /**
     * 首次就诊科室id
     *
     * @type {number}
     * @memberof Register
     */
    firstDeptId?: number | null;

    /**
     * 首次就诊医生id
     *
     * @type {number}
     * @memberof Register
     */
    firstDoctorId?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof Register
     */
    isNoCard?: YesNoEnum;

    /**
     * 险种标志
     *
     * @type {string}
     * @memberof Register
     */
    insuranceSign?: string | null;

    /**
     * 门诊号 第一次就诊的流水号
     *
     * @type {string}
     * @memberof Register
     */
    outpatientNo?: string | null;

    /**
     * 就医类别
     *
     * @type {string}
     * @memberof Register
     */
    medTreatCategory?: string | null;

    /**
     * 发病日期
     *
     * @type {Date}
     * @memberof Register
     */
    onsetDate?: Date | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof Register
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof Register
     */
    diagnosticName?: string | null;

    /**
     * 病种代码
     *
     * @type {string}
     * @memberof Register
     */
    diseaseTypeCode?: string | null;

    /**
     * 病种名称
     *
     * @type {string}
     * @memberof Register
     */
    diseaseTypeName?: string | null;

    /**
     * 门诊急诊转诊标志
     *
     * @type {string}
     * @memberof Register
     */
    outEmeReferralSign?: string | null;

    /**
     * 外伤标志
     *
     * @type {string}
     * @memberof Register
     */
    traumaSign?: string | null;

    /**
     * 涉及第三方标志
     *
     * @type {string}
     * @memberof Register
     */
    thirdPartySign?: string | null;
}
