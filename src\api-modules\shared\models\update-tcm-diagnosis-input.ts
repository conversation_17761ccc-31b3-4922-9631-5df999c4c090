/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医诊断更新输入参数
 *
 * @export
 * @interface UpdateTcmDiagnosisInput
 */
export interface UpdateTcmDiagnosisInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateTcmDiagnosisInput
     */
    id: number;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof UpdateTcmDiagnosisInput
     */
    tcmDiagnosisCode: string;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof UpdateTcmDiagnosisInput
     */
    tcmDiagnosisName: string;

    /**
     * 版本
     *
     * @type {string}
     * @memberof UpdateTcmDiagnosisInput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateTcmDiagnosisInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateTcmDiagnosisInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateTcmDiagnosisInput
     */
    orderNo?: number | null;
}
