/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListSchedulingPlanOutput } from './sql-sugar-paged-list-scheduling-plan-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListSchedulingPlanOutput
 */
export interface AdminResultSqlSugarPagedListSchedulingPlanOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListSchedulingPlanOutput}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    result?: SqlSugarPagedListSchedulingPlanOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListSchedulingPlanOutput
     */
    time?: Date;
}
