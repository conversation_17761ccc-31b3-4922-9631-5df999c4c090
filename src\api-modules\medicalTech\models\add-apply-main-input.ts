/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单增加输入参数
 *
 * @export
 * @interface AddApplyMainInput
 */
export interface AddApplyMainInput {

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    registerId: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof AddApplyMainInput
     */
    visitNo: string;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    patientId: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    itemId: number;

    /**
     * 数量
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    quantity: number;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof AddApplyMainInput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof AddApplyMainInput
     */
    executeDeptAddress?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    frequencyId: number;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof AddApplyMainInput
     */
    frequencyName: string;

    /**
     * 天数
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    days: number;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    flag: number;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof AddApplyMainInput
     */
    urgencyLevel?: number | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof AddApplyMainInput
     */
    prescriptionType?: string | null;
}
