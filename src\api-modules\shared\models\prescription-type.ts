/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方类型表
 *
 * @export
 * @interface PrescriptionType
 */
export interface PrescriptionType {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionType
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionType
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionType
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    wubiCode?: string | null;

    /**
     * 可使用的收费类别
     *
     * @type {Array<number>}
     * @memberof PrescriptionType
     */
    chargeCategorys?: Array<number> | null;

    /**
     * 处方条目
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    prescriptionEntries?: number | null;

    /**
     * 状态
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    status?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof PrescriptionType
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionType
     */
    remark?: string | null;
}
