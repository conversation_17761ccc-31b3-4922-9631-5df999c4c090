﻿import {useBaseApi} from '/@/api/base';

// 药品持有人表接口服务
export const useDrugHolderApi = () => {
	const baseApi = useBaseApi("drugHolder");
	return {
		// 分页查询药品持有人表
		page: baseApi.page,
		// 查看药品持有人表详细
		detail: baseApi.detail,
		// 新增药品持有人表
		add: baseApi.add,
		// 更新药品持有人表
		update: baseApi.update,
		// 设置药品持有人表状态
		setStatus: baseApi.setStatus,
		// 删除药品持有人表
		delete: baseApi.delete,
		// 批量删除药品持有人表
		batchDelete: baseApi.batchDelete,
		// 导出药品持有人表数据
		exportData: baseApi.exportData,
		// 导入药品持有人表数据
		importData: baseApi.importData,
		// 下载药品持有人表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 药品持有人表实体
export interface DrugHolder {
	// 主键Id
	id: number;
	// 持有人名称
	holderName: string;
	// 持有人名称拼音
	holderNamePinyin: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}