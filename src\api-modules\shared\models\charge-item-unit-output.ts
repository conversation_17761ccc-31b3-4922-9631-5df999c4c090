/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费项目单位输出参数
 *
 * @export
 * @interface ChargeItemUnitOutput
 */
export interface ChargeItemUnitOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ChargeItemUnitOutput
     */
    id?: number;

    /**
     * 单位编码
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    unitCode?: string | null;

    /**
     * 单位名称
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    unitName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    wubiCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeItemUnitOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeItemUnitOutput
     */
    orderNo?: number | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItemUnitOutput
     */
    tenantId?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItemUnitOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItemUnitOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItemUnitOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItemUnitOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItemUnitOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItemUnitOutput
     */
    isDelete?: boolean;
}
