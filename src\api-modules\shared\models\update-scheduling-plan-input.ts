/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 排班计划更新输入参数
 *
 * @export
 * @interface UpdateSchedulingPlanInput
 */
export interface UpdateSchedulingPlanInput {

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateSchedulingPlanInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateSchedulingPlanInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateSchedulingPlanInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateSchedulingPlanInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateSchedulingPlanInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    tenantId?: number | null;

    /**
     * 医生id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    doctorId?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    timePeriodId?: number | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    regCategoryId?: number | null;

    /**
     * 限号数
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    regLimit?: number | null;

    /**
     * 限预约号数
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    appLimit?: number | null;

    /**
     * 已挂号数
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    regNumber?: number | null;

    /**
     * 已预约号数
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    appNumber?: number | null;

    /**
     * 门诊日期
     *
     * @type {Date}
     * @memberof UpdateSchedulingPlanInput
     */
    outpatientDate?: Date;

    /**
     * 科室id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    deptId?: number | null;

    /**
     * 星期几
     *
     * @type {string}
     * @memberof UpdateSchedulingPlanInput
     */
    weekDay?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateSchedulingPlanInput
     */
    remark?: string | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateSchedulingPlanInput
     */
    id: number;
}
