/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 中医证型分页查询输入参数
 *
 * @export
 * @interface PageTcmSyndromeInput
 */
export interface PageTcmSyndromeInput {

    /**
     * @type {Search}
     * @memberof PageTcmSyndromeInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageTcmSyndromeInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageTcmSyndromeInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageTcmSyndromeInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    descStr?: string | null;

    /**
     * 中医证型编码
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    tcmSyndromeCode?: string | null;

    /**
     * 中医证型名称
     *
     * @type {string}
     * @memberof PageTcmSyndromeInput
     */
    tcmSyndromeName?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageTcmSyndromeInput
     */
    selectKeyList?: Array<number> | null;
}
