/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { FeeCategory } from './fee-category';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultFeeCategory
 */
export interface AdminResultFeeCategory {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultFeeCategory
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultFeeCategory
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultFeeCategory
     */
    message?: string | null;

    /**
     * @type {FeeCategory}
     * @memberof AdminResultFeeCategory
     */
    result?: FeeCategory;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultFeeCategory
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultFeeCategory
     */
    time?: Date;
}
