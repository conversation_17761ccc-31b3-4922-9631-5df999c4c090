/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 核算类别分页查询输入参数
 *
 * @export
 * @interface PageCalculateCategoryInput
 */
export interface PageCalculateCategoryInput {

    /**
     * @type {Search}
     * @memberof PageCalculateCategoryInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageCalculateCategoryInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageCalculateCategoryInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageCalculateCategoryInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageCalculateCategoryInput
     */
    name?: string | null;
}
