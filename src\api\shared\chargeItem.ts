﻿import { useBaseApi } from '/@/api/base';

// 收费项目接口服务
export const useChargeItemApi = () => {
	const baseApi = useBaseApi('chargeItem');
	return {
		// 分页查询收费项目
		page: baseApi.page,
		// 查询收费项目列表
		list: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'list',
					method: 'post',
					data,
				},
				cancel
			);
		},
		// 查看收费项目详细
		detail: baseApi.detail,
		// 新增收费项目
		add: baseApi.add,
		// 更新收费项目
		update: baseApi.update,
		// 设置收费项目状态
		setStatus: baseApi.setStatus,
		// 删除收费项目
		delete: baseApi.delete,
		// 批量删除收费项目
		batchDelete: baseApi.batchDelete,
		// 导出收费项目数据
		exportData: baseApi.exportData,
		// 导入收费项目数据
		importData: baseApi.importData,
		// 下载收费项目数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	};
};

// 收费项目实体
export interface ChargeItem {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 单位
	unit: string;
	// 规格
	spec: string;
	// 单价
	price?: number;
	// 进价
	purchasePrice: number;
	// 型号
	model: string;
	// 批件产品名称
	approvalName: string;
	// 产地
	producer: string;
	// 生产厂家
	manufacturer: string;
	// 注册证号
	registrationNumber: string;
	// 物价编码
	priceCode: string;
	// 收费类别
	chargeCategoryId?: number;
	// 核算类别
	calculateCategoryId?: number;
	// 电子发票费用类别
	dzfpChargeCategory?: string;
	// 病案首页费用类别
	basyChargeCategory?: string;
	// 是否高值耗材
	highValue: number;
	// 是否单用
	useSeparately?: number;
	// 是否上传地纬
	uploadDw?: number;
	// 频次
	frequencyId: number;
	// 样本类型
	sampleType: string;
	// 护理等级
	nurseLevel: string;
	// 检查类别
	checkCategoryId: number;
	// 退费模式
	refundMode: number;
	// 是否套餐
	package: number;
	// 使用科室
	useDepts: number;
	// 使用范围
	usageScope?: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 检查部位
	checkPointId: number;
}
