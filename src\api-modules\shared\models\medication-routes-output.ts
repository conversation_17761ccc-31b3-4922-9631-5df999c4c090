/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 给药途径输出参数
 *
 * @export
 * @interface MedicationRoutesOutput
 */
export interface MedicationRoutesOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    id?: number;

    /**
     * 途径编码
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    routeCode?: string | null;

    /**
     * 途径名称
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    routeName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    wubiCode?: string | null;

    /**
     * 缩写
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    abbreviation?: string | null;

    /**
     * 分类
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    routeCategory?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    remark?: string | null;

    /**
     * 状态
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    status?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof MedicationRoutesOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof MedicationRoutesOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof MedicationRoutesOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof MedicationRoutesOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof MedicationRoutesOutput
     */
    tenantId?: number | null;
}
