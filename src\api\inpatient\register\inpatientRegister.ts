﻿import {useBaseApi} from '/@/api/base';

// 住院登记接口服务
export const useInpatientRegisterApi = () => {
	const baseApi = useBaseApi("inpatientRegister");
	return {
		// 分页查询住院登记
		page: baseApi.page,
		// 查看住院登记详细
		detail: baseApi.detail,
		// 新增住院登记
		add: baseApi.add,
		// 更新住院登记
		update: baseApi.update,
		// 删除住院登记
		delete: baseApi.delete,
		// 批量删除住院登记
		batchDelete: baseApi.batchDelete,
		// 导出住院登记数据
		exportData: baseApi.exportData,
		// 导入住院登记数据
		importData: baseApi.importData,
		// 下载住院登记数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		getDiagnosticMap: function (key: any,top:number=20, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'getDiagnosticMap',
                method: 'get',
                data: { key ,top},
            }, cancel);
        },
	}
}

// 住院登记实体
export interface InpatientRegister {
	// 主键Id
	id: number;
	// 患者ID
	patientId: number;
	// 患者编号
	patientNo: string;
	// 患者姓名
	patientName: string;
	// 证件类型
	cardType: string;
	// 证件类型
	idCardType: number;
	// 证件号码
	idCardNo: string;
	// 保险号
	insuranceNo: string;
	// 就诊卡ID
	medicalCardId: number;
	// 就诊卡号
	medicalCardNo: string;
	// 门诊号
	outpatientNo: string;
	// 住院号
	inpatientNo: string;
	// 住院流水号
	inpatientSerialNo: string;
	// 住院次数
	inpatientTimes: number;
	// 病案号
	medicalRecordNo: string;
	// 费别ID
	feeId: number;
	// 医生ID
	doctorId: number;
	// 医生姓名
	doctorName: string;
	// 科室ID
	deptId: number;
	// 科室名称
	deptName: string;
	// 主治医生ID
	mainDoctorId: number;
	// 主治医生姓名
	mainDoctorName: string;
	// 入院诊断代码
	inpatientDiagnosticCode: string;
	// 入院诊断名称
	inpatientDiagnosticName: string;
	// 次要诊断代码
	secondaryDiagnosticCode: string;
	// 次要诊断名称
	secondaryDiagnosticName: string;
	// 入院途径
	inpatientWay: string;
	// 入院时间
	inpatientTime: string;
	// 是否允许欠费
	allowArrears: boolean;
	// 欠费上限
	arrearsLimit: number;
	// 担保人
	guaranteePerson: string;
	// 新生儿出生体重g
	newbornBirthWeight1: number;
	// 新生儿出生体重单位g
	newbornBirthWeight2: number;
	// 新生儿出生体重单位g
	newbornBirthWeight3: number;
	// 新生儿入院体重g
	newbornInpatientWeight: number;
	// 是否有医保卡
	hasMedicalInsurance: number;
	// 状态
	status: number;
	// 创建组织ID
	createOrgId: number;
	// 创建组织名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}