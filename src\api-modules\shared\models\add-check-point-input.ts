/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查部位增加输入参数
 *
 * @export
 * @interface AddCheckPointInput
 */
export interface AddCheckPointInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddCheckPointInput
     */
    name: string;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof AddCheckPointInput
     */
    checkCategoryId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof AddCheckPointInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddCheckPointInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddCheckPointInput
     */
    remark?: string | null;
}
