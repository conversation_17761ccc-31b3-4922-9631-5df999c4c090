/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListPrescriptionTypeOutput } from './sql-sugar-paged-list-prescription-type-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListPrescriptionTypeOutput
 */
export interface AdminResultSqlSugarPagedListPrescriptionTypeOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListPrescriptionTypeOutput}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    result?: SqlSugarPagedListPrescriptionTypeOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListPrescriptionTypeOutput
     */
    time?: Date;
}
