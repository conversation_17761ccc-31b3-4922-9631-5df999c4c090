/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItem } from './charge-item';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultListChargeItem
 */
export interface AdminResultListChargeItem {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultListChargeItem
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultListChargeItem
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultListChargeItem
     */
    message?: string | null;

    /**
     * 数据
     *
     * @type {Array<ChargeItem>}
     * @memberof AdminResultListChargeItem
     */
    result?: Array<ChargeItem> | null;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultListChargeItem
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultListChargeItem
     */
    time?: Date;
}
