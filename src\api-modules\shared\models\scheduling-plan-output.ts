/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 排班计划输出参数
 *
 * @export
 * @interface SchedulingPlanOutput
 */
export interface SchedulingPlanOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof SchedulingPlanOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof SchedulingPlanOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof SchedulingPlanOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    tenantId?: number | null;

    /**
     * 医生id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    doctorId?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    timePeriodId?: number | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    regCategoryId?: number | null;

    /**
     * 限号数
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    regLimit?: number | null;

    /**
     * 限预约号数
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    appLimit?: number | null;

    /**
     * 已挂号数
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    regNumber?: number | null;

    /**
     * 已预约号数
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    appNumber?: number | null;

    /**
     * 门诊日期
     *
     * @type {Date}
     * @memberof SchedulingPlanOutput
     */
    outpatientDate?: Date;

    /**
     * 科室id
     *
     * @type {number}
     * @memberof SchedulingPlanOutput
     */
    deptId?: number | null;

    /**
     * 星期几
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    weekDay?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    remark?: string | null;

    /**
     * 医生编码
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    doctorCode?: string | null;

    /**
     * 医生名称
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    doctorName?: string | null;

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    timePeriodName?: string | null;

    /**
     * 号别名称
     *
     * @type {string}
     * @memberof SchedulingPlanOutput
     */
    regCategoryName?: string | null;
}
