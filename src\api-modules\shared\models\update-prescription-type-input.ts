/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 处方类型更新输入参数
 *
 * @export
 * @interface UpdatePrescriptionTypeInput
 */
export interface UpdatePrescriptionTypeInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionTypeInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionTypeInput
     */
    name: string;

    /**
     * 可使用的收费类别
     *
     * @type {Array<number>}
     * @memberof UpdatePrescriptionTypeInput
     */
    chargeCategorys?: Array<number> | null;

    /**
     * 处方条目
     *
     * @type {number}
     * @memberof UpdatePrescriptionTypeInput
     */
    prescriptionEntries?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdatePrescriptionTypeInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdatePrescriptionTypeInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdatePrescriptionTypeInput
     */
    remark?: string | null;
}
