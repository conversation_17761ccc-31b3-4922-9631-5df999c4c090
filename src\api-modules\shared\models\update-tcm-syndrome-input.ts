/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医证型更新输入参数
 *
 * @export
 * @interface UpdateTcmSyndromeInput
 */
export interface UpdateTcmSyndromeInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateTcmSyndromeInput
     */
    id: number;

    /**
     * 中医证型编码
     *
     * @type {string}
     * @memberof UpdateTcmSyndromeInput
     */
    tcmSyndromeCode: string;

    /**
     * 中医证型名称
     *
     * @type {string}
     * @memberof UpdateTcmSyndromeInput
     */
    tcmSyndromeName: string;

    /**
     * 版本
     *
     * @type {string}
     * @memberof UpdateTcmSyndromeInput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateTcmSyndromeInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateTcmSyndromeInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateTcmSyndromeInput
     */
    orderNo?: number | null;
}
