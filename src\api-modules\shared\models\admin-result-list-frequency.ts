/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Frequency } from './frequency';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultListFrequency
 */
export interface AdminResultListFrequency {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultListFrequency
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultListFrequency
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultListFrequency
     */
    message?: string | null;

    /**
     * 数据
     *
     * @type {Array<Frequency>}
     * @memberof AdminResultListFrequency
     */
    result?: Array<Frequency> | null;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultListFrequency
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultListFrequency
     */
    time?: Date;
}
