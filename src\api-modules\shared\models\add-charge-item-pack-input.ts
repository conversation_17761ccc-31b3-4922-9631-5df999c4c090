/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 单项套餐增加输入参数
 *
 * @export
 * @interface AddChargeItemPackInput
 */
export interface AddChargeItemPackInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddChargeItemPackInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddChargeItemPackInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddChargeItemPackInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddChargeItemPackInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddChargeItemPackInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    tenantId?: number | null;

    /**
     * 套餐Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    packId?: number | null;

    /**
     * 收费项目Id
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    chargeItemId?: number | null;

    /**
     * 收费项目数量
     *
     * @type {number}
     * @memberof AddChargeItemPackInput
     */
    chargeItemQuantity?: number | null;
}
