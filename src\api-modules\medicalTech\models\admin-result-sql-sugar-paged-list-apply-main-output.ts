/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListApplyMainOutput } from './sql-sugar-paged-list-apply-main-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListApplyMainOutput
 */
export interface AdminResultSqlSugarPagedListApplyMainOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListApplyMainOutput}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    result?: SqlSugarPagedListApplyMainOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListApplyMainOutput
     */
    time?: Date;
}
