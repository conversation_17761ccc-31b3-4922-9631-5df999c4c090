/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方主表
 *
 * @export
 * @interface PrescriptionMain
 */
export interface PrescriptionMain {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionMain
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    tenantId?: number | null;

    /**
     * 处方号
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    prescriptionNo?: string | null;

    /**
     * 处方时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    prescriptionTime?: Date | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    prescriptionType?: string | null;

    /**
     * 处方名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    prescriptionName?: string | null;

    /**
     * 西药处方类型
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    wstrnMdcnPrescriptionType?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    patientName?: string | null;

    /**
     * 挂号Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    registerId?: number | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    billingDeptId?: number | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生签名
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    billingDoctorSign?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    refundTime?: Date | null;

    /**
     * 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    status?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    remark?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnosticName?: string | null;

    /**
     * 次诊断1编码
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnostic1Code?: string | null;

    /**
     * 次诊断1名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnostic1Name?: string | null;

    /**
     * 次诊断2编码
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnostic2Code?: string | null;

    /**
     * 次诊断2名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    diagnostic2Name?: string | null;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    tcmDiagnosticCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    tcmDiagnosticName?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    isPrint?: number | null;

    /**
     * 中药付数
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    herbsQuantity?: number | null;

    /**
     * 中药煎法
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    herbsDecoction?: string | null;

    /**
     * 是否代煎
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    isDecoction?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof PrescriptionMain
     */
    printTime?: Date | null;

    /**
     * 收费主表Id
     *
     * @type {number}
     * @memberof PrescriptionMain
     */
    chargeMainId?: number | null;

    /**
     * 退费发票号
     *
     * @type {string}
     * @memberof PrescriptionMain
     */
    refundInvoiceNumber?: string | null;
}
