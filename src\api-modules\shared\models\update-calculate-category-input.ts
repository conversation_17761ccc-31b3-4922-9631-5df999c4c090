/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 核算类别更新输入参数
 *
 * @export
 * @interface UpdateCalculateCategoryInput
 */
export interface UpdateCalculateCategoryInput {

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateCalculateCategoryInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateCalculateCategoryInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateCalculateCategoryInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateCalculateCategoryInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateCalculateCategoryInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateCalculateCategoryInput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof UpdateCalculateCategoryInput
     */
    wubiCode?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateCalculateCategoryInput
     */
    status: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateCalculateCategoryInput
     */
    orderNo?: number | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateCalculateCategoryInput
     */
    id: number;
}
