/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方明细表
 *
 * @export
 * @interface PrescriptionDetail
 */
export interface PrescriptionDetail {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionDetail
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionDetail
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionDetail
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    tenantId?: number | null;

    /**
     * 处方主表Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    prescriptionId?: number | null;

    /**
     * 药品Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    drugId?: number | null;

    /**
     * 药品编码
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    drugCode?: string | null;

    /**
     * 药品名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    drugName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    quantity?: number | null;

    /**
     * 单次量
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    singleDose?: number | null;

    /**
     * 单次量单位
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    singleDoseUnit?: string | null;

    /**
     * 给药途径Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    medicationRoutesId?: number | null;

    /**
     * 给药途径名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    medicationRoutesName?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    frequencyName?: string | null;

    /**
     * 用药天数
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    medicationDays?: number | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    amount?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    manufacturer?: string | null;

    /**
     * 药房Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    pharmacyId?: number | null;

    /**
     * 药房名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    pharmacyName?: string | null;

    /**
     * 组标志
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    groupFlag?: string | null;

    /**
     * 组号
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    groupNo?: string | null;

    /**
     * 药品限制标志
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    drugLimitFlag?: number | null;

    /**
     * 药品待发标志
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    drugPendingFlag?: number | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    chargeCategoryId?: number | null;

    /**
     * 剂量单位
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    dosageUnit?: string | null;

    /**
     * 剂量值
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    dosageValue?: number | null;

    /**
     * 含量
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    contentValue?: number | null;

    /**
     * 含量单位
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    contentUnit?: string | null;

    /**
     * 门诊包装数量
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    outpatientPackageQuantity?: number | null;

    /**
     * 最小包装单位
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    minPackageUnit?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof PrescriptionDetail
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof PrescriptionDetail
     */
    refundTime?: Date | null;

    /**
     * 库存零售价
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    inventorySalePrice?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof PrescriptionDetail
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    ratioAuditStaffName?: string | null;

    /**
     * 用药方式 1治疗用药 2预防用药
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    medicationMethod?: number | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    medicineCode?: string | null;

    /**
     * 用法Id
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    usageId?: number | null;

    /**
     * 用法编码
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    usageCode?: string | null;

    /**
     * 用法名称
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    usageName?: string | null;

    /**
     * 是否皮试
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    isSkinTest?: number | null;

    /**
     * 皮试结果
     *
     * @type {number}
     * @memberof PrescriptionDetail
     */
    skinTestResults?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionDetail
     */
    remark?: string | null;
}
