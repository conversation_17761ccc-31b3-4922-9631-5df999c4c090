﻿import {useBaseApi} from '/@/api/base';

// 医生排班计划接口服务
export const useSchedulingPlanApi = () => {
	const baseApi = useBaseApi("registration/schedulingPlan");
	return {
		// 分页查询医生排班计划
		page: baseApi.page,
		// 查看医生排班计划详细
		detail: baseApi.detail,
		// 新增医生排班计划
		add: baseApi.add,
		// 更新医生排班计划
		update: baseApi.update,
		// 删除医生排班计划
		delete: baseApi.delete,
		// 批量删除医生排班计划
		batchDelete: baseApi.batchDelete,
		// 导出医生排班计划数据
		exportData: baseApi.exportData,
		// 导入医生排班计划数据
		importData: baseApi.importData,
		// 下载医生排班计划数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		//应用模板
		usePlanTemp: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "UsePlanTemp",
                method: 'post',
                data,
            }, cancel);
        },
		// 查询挂号科室
		getSchedulingDept: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getSchedulingDept",
                method: 'post',
                data,
            }, cancel);
        },
		// 查询挂号科室
		getSchedulingDoctor: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getSchedulingDoctor",
                method: 'post',
                data,
            }, cancel);
        },
		// 查询挂号类别
		getSchedulingDoctorRegCategory: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getSchedulingDoctorRegCategory",
                method: 'post',
                data,
            }, cancel);
        },
  }
}
// 医生排班计划实体
export interface SchedulingPlan {
	// 主键Id
	id: number;
	// 医生id
	doctorId: number;
	// 医生姓名
	doctorName: string;
	// 时间段id
	timePeriodId: number;
	// 时间段编码
	timePeriodCode: string;
	// 时间段名称
	timePeriodName: string;
	// 号别id
	regCategoryId: number;
	// 挂号类别名称
	regCategoryName: string;
	// 限号数
	regLimit: number;
	// 限预约号数
	appLimit: number;
	// 已挂号数
	regNumber: number;
	// 已预约号数
	appNumber: number;
	// 门诊日期
	outpatientDate: string;
	// 开始时间
	startTime: string;
	// 结束时间
	endTime: string;
	// 科室id
	deptId: number;
	// 科室名称
	deptName: string;
	// 星期几
	weekDay: string;
	// 诊室id
	roomId: number;
	// 诊室名称
	roomName: string;
	// ip地址
	ipAddress: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}