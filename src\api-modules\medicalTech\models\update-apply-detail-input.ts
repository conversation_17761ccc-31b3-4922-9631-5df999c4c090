/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单明细更新输入参数
 *
 * @export
 * @interface UpdateApplyDetailInput
 */
export interface UpdateApplyDetailInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    id: number;

    /**
     * 申请主表Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    applyMainId?: number | null;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    registerId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    visitNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    patientId?: number | null;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    itemCode?: string | null;

    /**
     * 药品或单项名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    unit?: number | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    number?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    flag?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    prescriptionType?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    specimenName?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    checkCategoryName?: string | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    urgencyLevel?: number | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof UpdateApplyDetailInput
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof UpdateApplyDetailInput
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    nationalstandardCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateApplyDetailInput
     */
    remark?: string | null;
}
