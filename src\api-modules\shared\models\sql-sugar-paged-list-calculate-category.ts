/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CalculateCategory } from './calculate-category';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListCalculateCategory
 */
export interface SqlSugarPagedListCalculateCategory {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<CalculateCategory>}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    items?: Array<CalculateCategory> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCalculateCategory
     */
    hasNextPage?: boolean;
}
