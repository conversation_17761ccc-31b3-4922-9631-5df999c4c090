/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 排班时间段分页查询输入参数
 *
 * @export
 * @interface PageTimePeriodInput
 */
export interface PageTimePeriodInput {

    /**
     * @type {Search}
     * @memberof PageTimePeriodInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageTimePeriodInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageTimePeriodInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageTimePeriodInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    descStr?: string | null;

    /**
     * 时间段编码
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    timePeriodCode?: string | null;

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof PageTimePeriodInput
     */
    timePeriodName?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageTimePeriodInput
     */
    selectKeyList?: Array<number> | null;
}
