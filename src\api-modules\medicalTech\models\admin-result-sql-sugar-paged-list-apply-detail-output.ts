/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListApplyDetailOutput } from './sql-sugar-paged-list-apply-detail-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListApplyDetailOutput
 */
export interface AdminResultSqlSugarPagedListApplyDetailOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListApplyDetailOutput}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    result?: SqlSugarPagedListApplyDetailOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListApplyDetailOutput
     */
    time?: Date;
}
