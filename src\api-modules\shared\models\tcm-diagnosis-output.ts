/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医诊断输出参数
 *
 * @export
 * @interface TcmDiagnosisOutput
 */
export interface TcmDiagnosisOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof TcmDiagnosisOutput
     */
    id?: number;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    tcmDiagnosisCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    tcmDiagnosisName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    wubiCode?: string | null;

    /**
     * 版本
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof TcmDiagnosisOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof TcmDiagnosisOutput
     */
    orderNo?: number | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof TcmDiagnosisOutput
     */
    tenantId?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof TcmDiagnosisOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof TcmDiagnosisOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof TcmDiagnosisOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof TcmDiagnosisOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof TcmDiagnosisOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof TcmDiagnosisOutput
     */
    isDelete?: boolean;
}
