/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemDto } from './charge-item-dto';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目输出参数
 *
 * @export
 * @interface ChargeItemDto
 */
export interface ChargeItemDto {

    /**
     * 收费类别
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    chargeCategoryIdFkColumn?: string | null;

    /**
     * 核算类别
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    calculateCategoryIdFkColumn?: string | null;

    /**
     * 频次
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    frequencyIdFkColumn?: string | null;

    /**
     * 检查类别
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    checkCategoryIdFkColumn?: string | null;

    /**
     * 检查部位
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    checkPointIdFkColumn?: string | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    wubiCode?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    unit?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    spec?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    price?: number | null;

    /**
     * 进价
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    purchasePrice?: number | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    model?: string | null;

    /**
     * 批件产品名称
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    approvalName?: string | null;

    /**
     * 产地
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    producer?: string | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    manufacturer?: string | null;

    /**
     * 注册证号
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    registrationNumber?: string | null;

    /**
     * 物价编码
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    priceCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    chargeCategoryId?: number | null;

    /**
     * 核算类别
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    calculateCategoryId?: number | null;

    /**
     * 电子发票费用类别
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    dzfpChargeCategory?: string | null;

    /**
     * 病案首页费用类别
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    basyChargeCategory?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemDto
     */
    highValue?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemDto
     */
    useSeparately?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemDto
     */
    uploadDw?: YesNoEnum;

    /**
     * 频次
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    frequencyId?: number | null;

    /**
     * 样本类型
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    sampleType?: string | null;

    /**
     * 护理等级
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    nurseLevel?: string | null;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    checkCategoryId?: number | null;

    /**
     * 退费模式
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    refundMode?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof ChargeItemDto
     */
    _package?: YesNoEnum;

    /**
     * 使用科室
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    useDepts?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof ChargeItemDto
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeItemDto
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItemDto
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItemDto
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItemDto
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItemDto
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    tenantId?: number | null;

    /**
     * 检查部位
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    checkPointId?: number | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ChargeItemDto
     */
    quantity?: number | null;

    /**
     * 套餐所包含的收费项目
     *
     * @type {Array<ChargeItemDto>}
     * @memberof ChargeItemDto
     */
    chargeItemPacks?: Array<ChargeItemDto> | null;
}
