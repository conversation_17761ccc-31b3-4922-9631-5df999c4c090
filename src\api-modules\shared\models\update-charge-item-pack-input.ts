/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 单项套餐更新输入参数
 *
 * @export
 * @interface UpdateChargeItemPackInput
 */
export interface UpdateChargeItemPackInput {

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateChargeItemPackInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateChargeItemPackInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateChargeItemPackInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateChargeItemPackInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateChargeItemPackInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    tenantId?: number | null;

    /**
     * 套餐Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    packId?: number | null;

    /**
     * 收费项目Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    chargeItemId?: number | null;

    /**
     * 收费项目数量
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    chargeItemQuantity?: number | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateChargeItemPackInput
     */
    id: number;
}
