// /* tslint:disable */
// /* eslint-disable */
// /**
//  * OutDoctor
//  * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
//  *
//  * OpenAPI spec version: 1.0.0
//  * 
//  *
//  * NOTE: This class is auto generated by the swagger code generator program.
//  * https://github.com/swagger-api/swagger-codegen.git
//  * Do not edit the class manually.
//  */

// import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
// import { Configuration } from '../configuration';
// // Some imports not used depending on template conditions
// // @ts-ignore
// import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
// import { AddMzRegisterInput } from '../models';
// import { AdminResultListRegister } from '../models';
// import { AdminResultListRegisterOutput } from '../models';
// import { AdminResultRegister } from '../models';
// import { AdminResultSqlSugarPagedListRegisterOutput } from '../models';
// import { RefundMzRegisterInput } from '../models';
// import { RegStatusEnum } from '../models';
// import { RegisterInput } from '../models';
// import { UpdateMzRegisterInput } from '../models';
// /**
//  * RegisterApi - axios parameter creator
//  * @export
//  */
// export const RegisterApiAxiosParamCreator = function (configuration?: Configuration) {
//     return {
//         /**
//          * 
//          * @summary 获取门诊挂号
//          * @param {number} id 主键Id
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             // verify required parameter 'id' is not null or undefined
//             if (id === null || id === undefined) {
//                 throw new RequiredError('id','Required parameter id was null or undefined when calling apiRegisterDetailGet.');
//             }
//             const localVarPath = `/api/register/detail`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             if (id !== undefined) {
//                 localVarQueryParameter['Id'] = id;
//             }

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 根据挂号状态查询挂号列表
//          * @param {RegStatusEnum} [status] 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterListByStatusGet: async (status?: RegStatusEnum, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             const localVarPath = `/api/register/listByStatus`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             if (status !== undefined) {
//                 localVarQueryParameter['Status'] = status;
//             }

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 获取门诊挂号列表
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             const localVarPath = `/api/register/list`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 分页查询就诊记录
//          * @param {RegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterPagePost: async (body?: RegisterInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             debugger;
//             const localVarPath = `/api/register/page`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
//             const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
//             localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 退号
//          * @param {RefundMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterRefundPost: async (body?: RefundMzRegisterInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             const localVarPath = `/api/register/refund`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
//             const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
//             localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 挂号
//          * @param {AddMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterRegisterPost: async (body?: AddMzRegisterInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             const localVarPath = `/api/register/register`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
//             const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
//             localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//         /**
//          * 
//          * @summary 更新挂号信息
//          * @param {UpdateMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         apiRegisterUpdatePost: async (body?: UpdateMzRegisterInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
//             const localVarPath = `/api/register/update`;
//             // use dummy base URL string because the URL constructor only accepts absolute URLs.
//             const localVarUrlObj = new URL(localVarPath, 'https://example.com');
//             let baseOptions;
//             if (configuration) {
//                 baseOptions = configuration.baseOptions;
//             }
//             const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
//             const localVarHeaderParameter = {} as any;
//             const localVarQueryParameter = {} as any;

//             // authentication Bearer required
//             // http bearer authentication required
//             if (configuration && configuration.accessToken) {
//                 const accessToken = typeof configuration.accessToken === 'function'
//                     ? await configuration.accessToken()
//                     : await configuration.accessToken;
//                 localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
//             }

//             localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

//             const query = new URLSearchParams(localVarUrlObj.search);
//             for (const key in localVarQueryParameter) {
//                 query.set(key, localVarQueryParameter[key]);
//             }
//             for (const key in options.params) {
//                 query.set(key, options.params[key]);
//             }
//             localVarUrlObj.search = (new URLSearchParams(query)).toString();
//             let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
//             localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
//             const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
//             localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

//             return {
//                 url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
//                 options: localVarRequestOptions,
//             };
//         },
//     }
// };

// /**
//  * RegisterApi - functional programming interface
//  * @export
//  */
// export const RegisterApiFp = function(configuration?: Configuration) {
//     return {
//         /**
//          * 
//          * @summary 获取门诊挂号
//          * @param {number} id 主键Id
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultRegister>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterDetailGet(id, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 根据挂号状态查询挂号列表
//          * @param {RegStatusEnum} [status] 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterListByStatusGet(status?: RegStatusEnum, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListRegisterOutput>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterListByStatusGet(status, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 获取门诊挂号列表
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListRegister>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterListGet(options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 分页查询就诊记录
//          * @param {RegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterPagePost(body?: RegisterInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListRegisterOutput>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterPagePost(body, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 退号
//          * @param {RefundMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterRefundPost(body?: RefundMzRegisterInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterRefundPost(body, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 挂号
//          * @param {AddMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterRegisterPost(body?: AddMzRegisterInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterRegisterPost(body, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//         /**
//          * 
//          * @summary 更新挂号信息
//          * @param {UpdateMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterUpdatePost(body?: UpdateMzRegisterInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
//             const localVarAxiosArgs = await RegisterApiAxiosParamCreator(configuration).apiRegisterUpdatePost(body, options);
//             return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
//                 const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
//                 return axios.request(axiosRequestArgs);
//             };
//         },
//     }
// };

// /**
//  * RegisterApi - factory interface
//  * @export
//  */
// export const RegisterApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
//     return {
//         /**
//          * 
//          * @summary 获取门诊挂号
//          * @param {number} id 主键Id
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultRegister>> {
//             return RegisterApiFp(configuration).apiRegisterDetailGet(id, options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 根据挂号状态查询挂号列表
//          * @param {RegStatusEnum} [status] 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterListByStatusGet(status?: RegStatusEnum, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListRegisterOutput>> {
//             return RegisterApiFp(configuration).apiRegisterListByStatusGet(status, options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 获取门诊挂号列表
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListRegister>> {
//             return RegisterApiFp(configuration).apiRegisterListGet(options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 分页查询就诊记录
//          * @param {RegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterPagePost(body?: RegisterInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListRegisterOutput>> {
//             return RegisterApiFp(configuration).apiRegisterPagePost(body, options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 退号
//          * @param {RefundMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterRefundPost(body?: RefundMzRegisterInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
//             return RegisterApiFp(configuration).apiRegisterRefundPost(body, options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 挂号
//          * @param {AddMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterRegisterPost(body?: AddMzRegisterInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
//             return RegisterApiFp(configuration).apiRegisterRegisterPost(body, options).then((request) => request(axios, basePath));
//         },
//         /**
//          * 
//          * @summary 更新挂号信息
//          * @param {UpdateMzRegisterInput} [body] 
//          * @param {*} [options] Override http request option.
//          * @throws {RequiredError}
//          */
//         async apiRegisterUpdatePost(body?: UpdateMzRegisterInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
//             return RegisterApiFp(configuration).apiRegisterUpdatePost(body, options).then((request) => request(axios, basePath));
//         },
//     };
// };

// /**
//  * RegisterApi - object-oriented interface
//  * @export
//  * @class RegisterApi
//  * @extends {BaseAPI}
//  */
// export class RegisterApi extends BaseAPI {
//     /**
//      * 
//      * @summary 获取门诊挂号
//      * @param {number} id 主键Id
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultRegister>> {
//         return RegisterApiFp(this.configuration).apiRegisterDetailGet(id, options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 根据挂号状态查询挂号列表
//      * @param {RegStatusEnum} [status] 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterListByStatusGet(status?: RegStatusEnum, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListRegisterOutput>> {
//         return RegisterApiFp(this.configuration).apiRegisterListByStatusGet(status, options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 获取门诊挂号列表
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListRegister>> {
//         return RegisterApiFp(this.configuration).apiRegisterListGet(options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 分页查询就诊记录
//      * @param {RegisterInput} [body] 
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterPagePost(body?: RegisterInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListRegisterOutput>> {
//         return RegisterApiFp(this.configuration).apiRegisterPagePost(body, options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 退号
//      * @param {RefundMzRegisterInput} [body] 
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterRefundPost(body?: RefundMzRegisterInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
//         return RegisterApiFp(this.configuration).apiRegisterRefundPost(body, options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 挂号
//      * @param {AddMzRegisterInput} [body] 
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterRegisterPost(body?: AddMzRegisterInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
//         return RegisterApiFp(this.configuration).apiRegisterRegisterPost(body, options).then((request) => request(this.axios, this.basePath));
//     }
//     /**
//      * 
//      * @summary 更新挂号信息
//      * @param {UpdateMzRegisterInput} [body] 
//      * @param {*} [options] Override http request option.
//      * @throws {RequiredError}
//      * @memberof RegisterApi
//      */
//     public async apiRegisterUpdatePost(body?: UpdateMzRegisterInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
//         return RegisterApiFp(this.configuration).apiRegisterUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
//     }
// }
