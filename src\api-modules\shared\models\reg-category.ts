/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 挂号类别表
 *
 * @export
 * @interface RegCategory
 */
export interface RegCategory {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof RegCategory
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof RegCategory
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof RegCategory
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof RegCategory
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof RegCategory
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof RegCategory
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof RegCategory
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof RegCategory
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof RegCategory
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof RegCategory
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof RegCategory
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof RegCategory
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof RegCategory
     */
    wubiCode?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof RegCategory
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof RegCategory
     */
    consultationFee?: number | null;

    /**
     * 收费项目id
     *
     * @type {number}
     * @memberof RegCategory
     */
    chargeItemId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof RegCategory
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof RegCategory
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof RegCategory
     */
    remark?: string | null;
}
