/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddTcmDiagnosisInput } from '../models';
import { AdminResultBoolean } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultSqlSugarPagedListTcmDiagnosisOutput } from '../models';
import { AdminResultTcmDiagnosis } from '../models';
import { DeleteTcmDiagnosisInput } from '../models';
import { PageTcmDiagnosisInput } from '../models';
import { SetTcmDiagnosisStatusInput } from '../models';
import { UpdateTcmDiagnosisInput } from '../models';
/**
 * TcmDiagnosisApi - axios parameter creator
 * @export
 */
export const TcmDiagnosisApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加中医诊断 ➕
         * @param {AddTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisAddPost: async (body?: AddTcmDiagnosisInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加诊断数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisAddTcmDiagnosisPost: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/addTcmDiagnosis`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除中医诊断 ❌
         * @param {Array<DeleteTcmDiagnosisInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisBatchDeletePost: async (body: Array<DeleteTcmDiagnosisInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiTcmDiagnosisBatchDeletePost.');
            }
            const localVarPath = `/api/tcmDiagnosis/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除中医诊断 ❌
         * @param {DeleteTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisDeletePost: async (body?: DeleteTcmDiagnosisInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取中医诊断详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiTcmDiagnosisDetailGet.');
            }
            const localVarPath = `/api/tcmDiagnosis/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出中医诊断记录 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisExportPost: async (body?: PageTcmDiagnosisInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载中医诊断数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入中医诊断记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询中医诊断 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisPagePost: async (body?: PageTcmDiagnosisInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置中医诊断状态 🚫
         * @param {SetTcmDiagnosisStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisSetStatusPost: async (body?: SetTcmDiagnosisStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新中医诊断 ✏️
         * @param {UpdateTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiTcmDiagnosisUpdatePost: async (body?: UpdateTcmDiagnosisInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/tcmDiagnosis/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TcmDiagnosisApi - functional programming interface
 * @export
 */
export const TcmDiagnosisApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加中医诊断 ➕
         * @param {AddTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisAddPost(body?: AddTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 添加诊断数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisAddTcmDiagnosisPost(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultBoolean>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisAddTcmDiagnosisPost(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除中医诊断 ❌
         * @param {Array<DeleteTcmDiagnosisInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisBatchDeletePost(body: Array<DeleteTcmDiagnosisInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除中医诊断 ❌
         * @param {DeleteTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisDeletePost(body?: DeleteTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取中医诊断详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultTcmDiagnosis>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出中医诊断记录 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisExportPost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载中医诊断数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入中医诊断记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询中医诊断 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisPagePost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListTcmDiagnosisOutput>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置中医诊断状态 🚫
         * @param {SetTcmDiagnosisStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisSetStatusPost(body?: SetTcmDiagnosisStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新中医诊断 ✏️
         * @param {UpdateTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisUpdatePost(body?: UpdateTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcmDiagnosisApiAxiosParamCreator(configuration).apiTcmDiagnosisUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * TcmDiagnosisApi - factory interface
 * @export
 */
export const TcmDiagnosisApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加中医诊断 ➕
         * @param {AddTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisAddPost(body?: AddTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加诊断数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisAddTcmDiagnosisPost(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultBoolean>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisAddTcmDiagnosisPost(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除中医诊断 ❌
         * @param {Array<DeleteTcmDiagnosisInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisBatchDeletePost(body: Array<DeleteTcmDiagnosisInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除中医诊断 ❌
         * @param {DeleteTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisDeletePost(body?: DeleteTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取中医诊断详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultTcmDiagnosis>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出中医诊断记录 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisExportPost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载中医诊断数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入中医诊断记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询中医诊断 🔖
         * @param {PageTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisPagePost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListTcmDiagnosisOutput>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置中医诊断状态 🚫
         * @param {SetTcmDiagnosisStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisSetStatusPost(body?: SetTcmDiagnosisStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新中医诊断 ✏️
         * @param {UpdateTcmDiagnosisInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiTcmDiagnosisUpdatePost(body?: UpdateTcmDiagnosisInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcmDiagnosisApiFp(configuration).apiTcmDiagnosisUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TcmDiagnosisApi - object-oriented interface
 * @export
 * @class TcmDiagnosisApi
 * @extends {BaseAPI}
 */
export class TcmDiagnosisApi extends BaseAPI {
    /**
     * 
     * @summary 增加中医诊断 ➕
     * @param {AddTcmDiagnosisInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisAddPost(body?: AddTcmDiagnosisInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 添加诊断数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisAddTcmDiagnosisPost(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultBoolean>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisAddTcmDiagnosisPost(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除中医诊断 ❌
     * @param {Array<DeleteTcmDiagnosisInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisBatchDeletePost(body: Array<DeleteTcmDiagnosisInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除中医诊断 ❌
     * @param {DeleteTcmDiagnosisInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisDeletePost(body?: DeleteTcmDiagnosisInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取中医诊断详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultTcmDiagnosis>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出中医诊断记录 🔖
     * @param {PageTcmDiagnosisInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisExportPost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载中医诊断数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入中医诊断记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询中医诊断 🔖
     * @param {PageTcmDiagnosisInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisPagePost(body?: PageTcmDiagnosisInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListTcmDiagnosisOutput>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置中医诊断状态 🚫
     * @param {SetTcmDiagnosisStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisSetStatusPost(body?: SetTcmDiagnosisStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新中医诊断 ✏️
     * @param {UpdateTcmDiagnosisInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcmDiagnosisApi
     */
    public async apiTcmDiagnosisUpdatePost(body?: UpdateTcmDiagnosisInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcmDiagnosisApiFp(this.configuration).apiTcmDiagnosisUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
