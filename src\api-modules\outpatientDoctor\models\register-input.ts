/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 门诊挂号分页查询输入参数
 *
 * @export
 * @interface RegisterInput
 */
export interface RegisterInput {

    /**
     * @type {Search}
     * @memberof RegisterInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof RegisterInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof RegisterInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof RegisterInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof RegisterInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof RegisterInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof RegisterInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof RegisterInput
     */
    descStr?: string | null;

    /**
     * 就诊流水号
     *
     * @type {number}
     * @memberof RegisterInput
     */
    id?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof RegisterInput
     */
    patientName?: string | null;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof RegisterInput
     */
    idCardNo?: string | null;

    /**
     * 就诊日期
     *
     * @type {Array<Date>}
     * @memberof RegisterInput
     */
    visitDate?: Array<Date> | null;
}
