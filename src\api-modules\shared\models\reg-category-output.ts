/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 挂号类别输出参数
 *
 * @export
 * @interface RegCategoryOutput
 */
export interface RegCategoryOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    wubiCode?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    consultationFee?: number | null;

    /**
     * 收费项目
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    chargeItemId?: number | null;

    /**
     * 收费项目 描述
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    chargeItemFkDisplayName?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof RegCategoryOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    remark?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof RegCategoryOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof RegCategoryOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof RegCategoryOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof RegCategoryOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof RegCategoryOutput
     */
    tenantId?: number | null;
}
