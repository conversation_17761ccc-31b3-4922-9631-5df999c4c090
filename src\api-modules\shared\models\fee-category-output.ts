/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedCategoryEnum } from './med-category-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
 /**
 * 费用类别输出参数
 *
 * @export
 * @interface FeeCategoryOutput
 */
export interface FeeCategoryOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    id?: number;

    /**
     * 编号
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    wubiCode?: string | null;

    /**
     * @type {MedCategoryEnum}
     * @memberof FeeCategoryOutput
     */
    medCategory?: MedCategoryEnum;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof FeeCategoryOutput
     */
    usageScope?: MedServiceCategoryEnum;

    /**
     * 医保id
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    medInsId?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof FeeCategoryOutput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * @type {StatusEnum}
     * @memberof FeeCategoryOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof FeeCategoryOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof FeeCategoryOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof FeeCategoryOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof FeeCategoryOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof FeeCategoryOutput
     */
    remark?: string | null;
}
