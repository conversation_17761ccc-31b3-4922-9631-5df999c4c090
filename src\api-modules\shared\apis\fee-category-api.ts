/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddFeeCategoryInput } from '../models';
import { AdminResultFeeCategory } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListFeeCategory } from '../models';
import { AdminResultSqlSugarPagedListFeeCategoryOutput } from '../models';
import { DeleteFeeCategoryInput } from '../models';
import { PageFeeCategoryInput } from '../models';
import { SetFeeCategoryStatusInput } from '../models';
import { UpdateFeeCategoryInput } from '../models';
/**
 * FeeCategoryApi - axios parameter creator
 * @export
 */
export const FeeCategoryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加费用类别 ➕
         * @param {AddFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryAddPost: async (body?: AddFeeCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除费用类别 ❌
         * @param {Array<DeleteFeeCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryBatchDeletePost: async (body: Array<DeleteFeeCategoryInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiFeeCategoryBatchDeletePost.');
            }
            const localVarPath = `/api/feeCategory/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除费用类别 ❌
         * @param {DeleteFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryDeletePost: async (body?: DeleteFeeCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取费用类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiFeeCategoryDetailGet.');
            }
            const localVarPath = `/api/feeCategory/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出费用类别记录 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryExportPost: async (body?: PageFeeCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载费用类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入费用类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取费用类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询费用类别 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryPagePost: async (body?: PageFeeCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置费用类别状态 🚫
         * @param {SetFeeCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategorySetStatusPost: async (body?: SetFeeCategoryStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新费用类别 ✏️
         * @param {UpdateFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiFeeCategoryUpdatePost: async (body?: UpdateFeeCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/feeCategory/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * FeeCategoryApi - functional programming interface
 * @export
 */
export const FeeCategoryApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加费用类别 ➕
         * @param {AddFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryAddPost(body?: AddFeeCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除费用类别 ❌
         * @param {Array<DeleteFeeCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryBatchDeletePost(body: Array<DeleteFeeCategoryInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除费用类别 ❌
         * @param {DeleteFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryDeletePost(body?: DeleteFeeCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取费用类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultFeeCategory>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出费用类别记录 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryExportPost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载费用类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入费用类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取费用类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListFeeCategory>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询费用类别 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryPagePost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListFeeCategoryOutput>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置费用类别状态 🚫
         * @param {SetFeeCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategorySetStatusPost(body?: SetFeeCategoryStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategorySetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新费用类别 ✏️
         * @param {UpdateFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryUpdatePost(body?: UpdateFeeCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await FeeCategoryApiAxiosParamCreator(configuration).apiFeeCategoryUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * FeeCategoryApi - factory interface
 * @export
 */
export const FeeCategoryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加费用类别 ➕
         * @param {AddFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryAddPost(body?: AddFeeCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除费用类别 ❌
         * @param {Array<DeleteFeeCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryBatchDeletePost(body: Array<DeleteFeeCategoryInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除费用类别 ❌
         * @param {DeleteFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryDeletePost(body?: DeleteFeeCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取费用类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultFeeCategory>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出费用类别记录 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryExportPost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载费用类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入费用类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取费用类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListFeeCategory>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询费用类别 🔖
         * @param {PageFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryPagePost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListFeeCategoryOutput>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置费用类别状态 🚫
         * @param {SetFeeCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategorySetStatusPost(body?: SetFeeCategoryStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategorySetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新费用类别 ✏️
         * @param {UpdateFeeCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiFeeCategoryUpdatePost(body?: UpdateFeeCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return FeeCategoryApiFp(configuration).apiFeeCategoryUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * FeeCategoryApi - object-oriented interface
 * @export
 * @class FeeCategoryApi
 * @extends {BaseAPI}
 */
export class FeeCategoryApi extends BaseAPI {
    /**
     * 
     * @summary 增加费用类别 ➕
     * @param {AddFeeCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryAddPost(body?: AddFeeCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除费用类别 ❌
     * @param {Array<DeleteFeeCategoryInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryBatchDeletePost(body: Array<DeleteFeeCategoryInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除费用类别 ❌
     * @param {DeleteFeeCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryDeletePost(body?: DeleteFeeCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取费用类别详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultFeeCategory>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出费用类别记录 🔖
     * @param {PageFeeCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryExportPost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载费用类别数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入费用类别记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取费用类别列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListFeeCategory>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询费用类别 🔖
     * @param {PageFeeCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryPagePost(body?: PageFeeCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListFeeCategoryOutput>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置费用类别状态 🚫
     * @param {SetFeeCategoryStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategorySetStatusPost(body?: SetFeeCategoryStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategorySetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新费用类别 ✏️
     * @param {UpdateFeeCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FeeCategoryApi
     */
    public async apiFeeCategoryUpdatePost(body?: UpdateFeeCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return FeeCategoryApiFp(this.configuration).apiFeeCategoryUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
