/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 挂号类别增加输入参数
 *
 * @export
 * @interface AddRegCategoryInput
 */
export interface AddRegCategoryInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddRegCategoryInput
     */
    name?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof AddRegCategoryInput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof AddRegCategoryInput
     */
    consultationFee?: number | null;

    /**
     * 收费项目
     *
     * @type {number}
     * @memberof AddRegCategoryInput
     */
    chargeItemId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof AddRegCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddRegCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddRegCategoryInput
     */
    remark?: string | null;
}
