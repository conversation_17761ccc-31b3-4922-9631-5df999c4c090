/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 处方明细表分页查询输入参数
 *
 * @export
 * @interface PagePrescriptionDetailInput
 */
export interface PagePrescriptionDetailInput {

    /**
     * @type {Search}
     * @memberof PagePrescriptionDetailInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PagePrescriptionDetailInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    descStr?: string | null;

    /**
     * 处方主表Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    prescriptionId?: number | null;

    /**
     * 药品Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    drugId?: number | null;

    /**
     * 药品编码
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    drugCode?: string | null;

    /**
     * 药品名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    drugName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    quantity?: number | null;

    /**
     * 单次量
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    singleDose?: number | null;

    /**
     * 单次量单位
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    singleDoseUnit?: string | null;

    /**
     * 给药途径Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    medicationRoutesId?: number | null;

    /**
     * 给药途径名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    medicationRoutesName?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    frequencyName?: string | null;

    /**
     * 用药天数
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    medicationDays?: number | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    amount?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    manufacturer?: string | null;

    /**
     * 药房Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    pharmacyId?: number | null;

    /**
     * 药房名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    pharmacyName?: string | null;

    /**
     * 组标志
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    groupFlag?: string | null;

    /**
     * 组号
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    groupNo?: string | null;

    /**
     * 药品限制标志
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    drugLimitFlag?: number | null;

    /**
     * 药品待发标志
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    drugPendingFlag?: number | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    chargeCategoryId?: number | null;

    /**
     * 剂量单位
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    dosageUnit?: string | null;

    /**
     * 剂量值
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    dosageValue?: number | null;

    /**
     * 含量
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    contentValue?: number | null;

    /**
     * 含量单位
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    contentUnit?: string | null;

    /**
     * 门诊包装数量
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    outpatientPackageQuantity?: number | null;

    /**
     * 最小包装单位
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    minPackageUnit?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionDetailInput
     */
    chargeTimeRange?: Array<Date> | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionDetailInput
     */
    refundTimeRange?: Array<Date> | null;

    /**
     * 库存零售价
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    inventorySalePrice?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间范围
     *
     * @type {Array<Date>}
     * @memberof PagePrescriptionDetailInput
     */
    ratioAuditTimeRange?: Array<Date> | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 用药方式 1治疗用药 2预防用药
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    medicationMethod?: number | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    medicineCode?: string | null;

    /**
     * 用法Id
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    usageId?: number | null;

    /**
     * 用法编码
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    usageCode?: string | null;

    /**
     * 用法名称
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    usageName?: string | null;

    /**
     * 是否皮试
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    isSkinTest?: number | null;

    /**
     * 皮试结果
     *
     * @type {number}
     * @memberof PagePrescriptionDetailInput
     */
    skinTestResults?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PagePrescriptionDetailInput
     */
    remark?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PagePrescriptionDetailInput
     */
    selectKeyList?: Array<number> | null;
}
