/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方主表增加输入参数
 *
 * @export
 * @interface AddPrescriptionMainInput
 */
export interface AddPrescriptionMainInput {

    /**
     * 处方号
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    prescriptionNo?: string | null;

    /**
     * 处方时间
     *
     * @type {Date}
     * @memberof AddPrescriptionMainInput
     */
    prescriptionTime?: Date | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    prescriptionType?: string | null;

    /**
     * 处方名称
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    prescriptionName?: string | null;

    /**
     * 西药处方类型
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    wstrnMdcnPrescriptionType?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    patientName?: string | null;

    /**
     * 挂号Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    registerId?: number | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    billingDeptId?: number | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生签名
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    billingDoctorSign?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof AddPrescriptionMainInput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof AddPrescriptionMainInput
     */
    refundTime?: Date | null;

    /**
     * 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    status?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    remark?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnosticName?: string | null;

    /**
     * 次诊断1编码
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnostic1Code?: string | null;

    /**
     * 次诊断1名称
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnostic1Name?: string | null;

    /**
     * 次诊断2编码
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnostic2Code?: string | null;

    /**
     * 次诊断2名称
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    diagnostic2Name?: string | null;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    tcmDiagnosticCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    tcmDiagnosticName?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    isPrint?: number | null;

    /**
     * 中药付数
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    herbsQuantity?: number | null;

    /**
     * 中药煎法
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    herbsDecoction?: string | null;

    /**
     * 是否代煎
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    isDecoction?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof AddPrescriptionMainInput
     */
    printTime?: Date | null;

    /**
     * 收费主表Id
     *
     * @type {number}
     * @memberof AddPrescriptionMainInput
     */
    chargeMainId?: number | null;

    /**
     * 退费发票号
     *
     * @type {string}
     * @memberof AddPrescriptionMainInput
     */
    refundInvoiceNumber?: string | null;
}
