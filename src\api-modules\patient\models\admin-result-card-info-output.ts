/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardInfoOutput } from './card-info-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultCardInfoOutput
 */
export interface AdminResultCardInfoOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultCardInfoOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultCardInfoOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultCardInfoOutput
     */
    message?: string | null;

    /**
     * @type {CardInfoOutput}
     * @memberof AdminResultCardInfoOutput
     */
    result?: CardInfoOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultCardInfoOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultCardInfoOutput
     */
    time?: Date;
}
