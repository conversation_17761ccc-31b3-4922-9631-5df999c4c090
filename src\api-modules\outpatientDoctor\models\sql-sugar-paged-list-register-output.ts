/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RegisterOutput } from './register-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListRegisterOutput
 */
export interface SqlSugarPagedListRegisterOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<RegisterOutput>}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    items?: Array<RegisterOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListRegisterOutput
     */
    hasNextPage?: boolean;
}
