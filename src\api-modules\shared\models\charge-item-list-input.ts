/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 
 *
 * @export
 * @interface ChargeItemListInput
 */
export interface ChargeItemListInput {

    /**
     * @type {Search}
     * @memberof ChargeItemListInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof ChargeItemListInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof ChargeItemListInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof ChargeItemListInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof ChargeItemListInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof ChargeItemListInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof ChargeItemListInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof ChargeItemListInput
     */
    descStr?: string | null;

    /**
     * 处方类型编码
     *
     * @type {string}
     * @memberof ChargeItemListInput
     */
    prescriptionTypeCode?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ChargeItemListInput
     */
    chargeCategoryId?: number | null;
}
