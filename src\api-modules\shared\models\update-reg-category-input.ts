/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 挂号类别更新输入参数
 *
 * @export
 * @interface UpdateRegCategoryInput
 */
export interface UpdateRegCategoryInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateRegCategoryInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateRegCategoryInput
     */
    name?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof UpdateRegCategoryInput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof UpdateRegCategoryInput
     */
    consultationFee?: number | null;

    /**
     * 收费项目
     *
     * @type {number}
     * @memberof UpdateRegCategoryInput
     */
    chargeItemId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateRegCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateRegCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateRegCategoryInput
     */
    remark?: string | null;
}
