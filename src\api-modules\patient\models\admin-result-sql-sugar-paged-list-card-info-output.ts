/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListCardInfoOutput } from './sql-sugar-paged-list-card-info-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListCardInfoOutput
 */
export interface AdminResultSqlSugarPagedListCardInfoOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListCardInfoOutput}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    result?: SqlSugarPagedListCardInfoOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListCardInfoOutput
     */
    time?: Date;
}
