/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 处方类型分页查询输入参数
 *
 * @export
 * @interface PagePrescriptionTypeInput
 */
export interface PagePrescriptionTypeInput {

    /**
     * @type {Search}
     * @memberof PagePrescriptionTypeInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PagePrescriptionTypeInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PagePrescriptionTypeInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PagePrescriptionTypeInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PagePrescriptionTypeInput
     */
    name?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PagePrescriptionTypeInput
     */
    selectKeyList?: Array<number> | null;
}
