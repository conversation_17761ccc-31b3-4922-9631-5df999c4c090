/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 核算类别增加输入参数
 *
 * @export
 * @interface AddCalculateCategoryInput
 */
export interface AddCalculateCategoryInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddCalculateCategoryInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddCalculateCategoryInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddCalculateCategoryInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddCalculateCategoryInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddCalculateCategoryInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddCalculateCategoryInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddCalculateCategoryInput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof AddCalculateCategoryInput
     */
    wubiCode?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddCalculateCategoryInput
     */
    status: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddCalculateCategoryInput
     */
    orderNo?: number | null;
}
