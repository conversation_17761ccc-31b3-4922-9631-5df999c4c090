﻿import {useBaseApi} from '/@/api/base';

// 收费项目单位接口服务
export const useChargeItemUnitApi = () => {
	const baseApi = useBaseApi("chargeItemUnit");
	return {
		// 分页查询收费项目单位
		page: baseApi.page,
		// 查看收费项目单位详细
		detail: baseApi.detail,
		// 新增收费项目单位
		add: baseApi.add,
		// 更新收费项目单位
		update: baseApi.update,
		// 设置收费项目单位状态
		setStatus: baseApi.setStatus,
		// 删除收费项目单位
		delete: baseApi.delete,
		// 批量删除收费项目单位
		batchDelete: baseApi.batchDelete,
		// 导出收费项目单位数据
		exportData: baseApi.exportData,
		// 导入收费项目单位数据
		importData: baseApi.importData,
		// 下载收费项目单位数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 收费项目单位实体
export interface ChargeItemUnit {
	// 主键Id
	id: number;
	// 单位编码
	unitCode: string;
	// 单位名称
	unitName?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 租户Id
	tenantId: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}