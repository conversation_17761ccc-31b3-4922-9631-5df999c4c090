/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddCheckCategoryInput } from '../models';
import { AdminResultCheckCategory } from '../models';
import { AdminResultDictionaryStringObject } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListCheckCategory } from '../models';
import { AdminResultSqlSugarPagedListCheckCategoryOutput } from '../models';
import { DeleteCheckCategoryInput } from '../models';
import { DropdownDataCheckCategoryInput } from '../models';
import { PageCheckCategoryInput } from '../models';
import { SetCheckCategoryStatusInput } from '../models';
import { UpdateCheckCategoryInput } from '../models';
/**
 * CheckCategoryApi - axios parameter creator
 * @export
 */
export const CheckCategoryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加检查类别 ➕
         * @param {AddCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryAddPost: async (body?: AddCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除检查类别 ❌
         * @param {Array<DeleteCheckCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryBatchDeletePost: async (body: Array<DeleteCheckCategoryInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiCheckCategoryBatchDeletePost.');
            }
            const localVarPath = `/api/checkCategory/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除检查类别 ❌
         * @param {DeleteCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryDeletePost: async (body?: DeleteCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取检查类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiCheckCategoryDetailGet.');
            }
            const localVarPath = `/api/checkCategory/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryDropdownDataPost: async (body?: DropdownDataCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/dropdownData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出检查类别记录 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryExportPost: async (body?: PageCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载检查类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入检查类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取检查类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询检查类别 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryPagePost: async (body?: PageCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置检查类别状态 🚫
         * @param {SetCheckCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategorySetStatusPost: async (body?: SetCheckCategoryStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新检查类别 ✏️
         * @param {UpdateCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCheckCategoryUpdatePost: async (body?: UpdateCheckCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/checkCategory/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CheckCategoryApi - functional programming interface
 * @export
 */
export const CheckCategoryApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加检查类别 ➕
         * @param {AddCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryAddPost(body?: AddCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除检查类别 ❌
         * @param {Array<DeleteCheckCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryBatchDeletePost(body: Array<DeleteCheckCategoryInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除检查类别 ❌
         * @param {DeleteCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDeletePost(body?: DeleteCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取检查类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultCheckCategory>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDropdownDataPost(body?: DropdownDataCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDictionaryStringObject>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryDropdownDataPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出检查类别记录 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryExportPost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载检查类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入检查类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取检查类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListCheckCategory>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询检查类别 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryPagePost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListCheckCategoryOutput>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置检查类别状态 🚫
         * @param {SetCheckCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategorySetStatusPost(body?: SetCheckCategoryStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategorySetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新检查类别 ✏️
         * @param {UpdateCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryUpdatePost(body?: UpdateCheckCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CheckCategoryApiAxiosParamCreator(configuration).apiCheckCategoryUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * CheckCategoryApi - factory interface
 * @export
 */
export const CheckCategoryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加检查类别 ➕
         * @param {AddCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryAddPost(body?: AddCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除检查类别 ❌
         * @param {Array<DeleteCheckCategoryInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryBatchDeletePost(body: Array<DeleteCheckCategoryInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除检查类别 ❌
         * @param {DeleteCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDeletePost(body?: DeleteCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取检查类别详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultCheckCategory>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryDropdownDataPost(body?: DropdownDataCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDictionaryStringObject>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryDropdownDataPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出检查类别记录 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryExportPost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载检查类别数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入检查类别记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取检查类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListCheckCategory>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询检查类别 🔖
         * @param {PageCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryPagePost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListCheckCategoryOutput>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置检查类别状态 🚫
         * @param {SetCheckCategoryStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategorySetStatusPost(body?: SetCheckCategoryStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategorySetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新检查类别 ✏️
         * @param {UpdateCheckCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCheckCategoryUpdatePost(body?: UpdateCheckCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CheckCategoryApiFp(configuration).apiCheckCategoryUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CheckCategoryApi - object-oriented interface
 * @export
 * @class CheckCategoryApi
 * @extends {BaseAPI}
 */
export class CheckCategoryApi extends BaseAPI {
    /**
     * 
     * @summary 增加检查类别 ➕
     * @param {AddCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryAddPost(body?: AddCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除检查类别 ❌
     * @param {Array<DeleteCheckCategoryInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryBatchDeletePost(body: Array<DeleteCheckCategoryInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除检查类别 ❌
     * @param {DeleteCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryDeletePost(body?: DeleteCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取检查类别详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultCheckCategory>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取下拉列表数据 🔖
     * @param {DropdownDataCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryDropdownDataPost(body?: DropdownDataCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDictionaryStringObject>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryDropdownDataPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出检查类别记录 🔖
     * @param {PageCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryExportPost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载检查类别数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入检查类别记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取检查类别列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListCheckCategory>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询检查类别 🔖
     * @param {PageCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryPagePost(body?: PageCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListCheckCategoryOutput>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置检查类别状态 🚫
     * @param {SetCheckCategoryStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategorySetStatusPost(body?: SetCheckCategoryStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategorySetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新检查类别 ✏️
     * @param {UpdateCheckCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CheckCategoryApi
     */
    public async apiCheckCategoryUpdatePost(body?: UpdateCheckCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CheckCategoryApiFp(this.configuration).apiCheckCategoryUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
