﻿import {useBaseApi} from '/@/api/base';

// 预交金接口服务
export const useAdvancePaymentApi = () => {
	const baseApi = useBaseApi("advancePayment");
	return {
		// 分页查询预交金
		page: baseApi.page,
		// 查看预交金详细
		detail: baseApi.detail,
		// 新增预交金
		add: baseApi.add,
		// 更新预交金
		update: baseApi.update,
		// 删除预交金
		delete: baseApi.delete,
		// 批量删除预交金
		batchDelete: baseApi.batchDelete,
		// 导出预交金数据
		exportData: baseApi.exportData,
		// 导入预交金数据
		importData: baseApi.importData,
		// 下载预交金数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 预交金实体
export interface AdvancePayment {
	// 主键Id
	id: number;
	// 收据号
	voucherNo: string;
	// 患者ID
	patientId: number;
	// 住院登记号
	registerId: number;
	// 住院号
	inpatientNo: string;
	// 住院流水号
	inpatientSerialNo: string;
	// 病案号
	medicalRecordNo: string;
	// 保险号码
	insuranceNo: string;
	// 预缴金额
	advanceAmount: number;
	// 大写金额
	advanceAmountChinese: string;
	// 缴费时间
	advanceTime: string;
	// 付款方式
	paymentMethod: string;
	// 付款记录ID
	paymentRecordId: number;
	// 状态
	status: number;
	// 创建组织ID
	createOrgId: number;
	// 创建组织名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}