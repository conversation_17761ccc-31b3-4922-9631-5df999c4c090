/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApplyMainOutput } from './apply-main-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListApplyMainOutput
 */
export interface SqlSugarPagedListApplyMainOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ApplyMainOutput>}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    items?: Array<ApplyMainOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListApplyMainOutput
     */
    hasNextPage?: boolean;
}
