import { useBaseApi } from '/@/api/base';

// 申请单处理接口服务(包括执行和处理)
export const useApplyProcessApi = () => {
	const baseApi = useBaseApi('applyProcess');
	return {
		// 分页查询申请单
		page: baseApi.page,
		// 查看申请单详细
		detail: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'detail',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//设置申请单状态 执行 取消执行 作废 预扣款
		setStatus: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'setStatus',
					method: 'post',
					data,
				},
				cancel
			);
		},
		// 收费
		charge: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'charge',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};
