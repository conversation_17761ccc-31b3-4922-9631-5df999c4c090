﻿import {useBaseApi} from '/@/api/base';

// 门诊退药接口服务
export const useOutpatientDrugRefundApi = () => {
	const baseApi = useBaseApi("outpatientDrugRefund");
	return {
		// 新增门诊退药
		submit: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'submit',
                method: 'post',
                data
            }, cancel);
        }
	
	}
}

// 门诊退药实体
export interface OutpatientDrugRefundRecord {
	// 主键Id
	id: number;
	// 退药单号
	refundNo: string;
	// 关联的发药记录ID
	sendRecordId: number;
	// 退药人ID
	refundUserId: number;
	// 退药人名称
	refundUserName: string;
	// 退药时间
	refundTime: string;
	// 退药申请id
	refundApplyId: number;
	// 审核完成时间
	auditTime: string;
	// 退药原因
	reason: string;
	// 药房ID
	storageId: number;
	// 药房名称
	storageName: string;
	// 患者ID
	patientId: number;
	// 患者名称
	patientName: string;
	// 就诊号
	visitNo: string;
	// 就诊id
	visitId: number;
	// 卡号
	cardNo: string;
	// 卡id
	cardId: number;
	// 处方ID
	prescriptionId: number;
	// 处方明细ID
	prescriptionDetailId: number;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 药品规格
	spec: string;
	// 药品单位
	unit: string;
	// 退药数量
	refundQuantity: number;
	// 零售价
	price: number;
	// 总退药金额
	refundAmount: number;
	// 批号
	batchNo: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}