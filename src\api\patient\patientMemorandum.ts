﻿import {useBaseApi} from '/@/api/base';

// 患者备忘录主表接口服务
export const usePatientMemorandumApi = () => {
	const baseApi = useBaseApi("patientMemorandum");
	return {
		// 分页查询患者备忘录主表
		page: baseApi.page,
		// 查看患者备忘录主表详细
		detail: baseApi.detail,
		// 新增患者备忘录主表
		add: baseApi.add,
		// 更新患者备忘录主表
		update: baseApi.update,
		// 删除患者备忘录主表
		delete: baseApi.delete,
		// 批量删除患者备忘录主表
		batchDelete: baseApi.batchDelete,
		// 导出患者备忘录主表数据
		exportData: baseApi.exportData,
		// 导入患者备忘录主表数据
		importData: baseApi.importData,
		// 下载患者备忘录主表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 患者备忘录主表实体
export interface PatientMemorandum {
	// 主键Id
	id: number;
	// 患者Id
	patientId?: number;
	// 患者姓名
	patientName: string;
	// 身份证号
	idCardNo: string;
	// 门诊号
	outpatientNo: string;
	// 备忘内容
	remark: string;
	// 创建机构Id
	createOrgId: number;
	// 创建机构名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}