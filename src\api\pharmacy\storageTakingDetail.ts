﻿import {useBaseApi} from '/@/api/base';

// 药品盘点明细表接口服务
export const useStorageTakingDetailApi = () => {
	const baseApi = useBaseApi("storageTakingDetail");
	return {
		// 分页查询药品盘点明细表
		page: baseApi.page,
		// 查看药品盘点明细表详细
		detail: baseApi.detail,
		// 新增药品盘点明细表
		add: baseApi.add,
		// 更新药品盘点明细表
		update: baseApi.update,
		// 删除药品盘点明细表
		delete: baseApi.delete,
		// 批量删除药品盘点明细表
		batchDelete: baseApi.batchDelete,
		// 导出药品盘点明细表数据
		exportData: baseApi.exportData,
		// 导入药品盘点明细表数据
		importData: baseApi.importData,
		// 下载药品盘点明细表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 药品盘点明细表实体
export interface StorageTakingDetail {
	// 主键Id
	id: number;
	// 盘点记录ID
	takingRecordId: number;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 现有数量
	currentQuantity: number;
	// 现有零售价
	currentSalePrice: number;
	// 现有零售总价
	totalCurrentSalePrice: number;
	// 盘点数量
	takingQuantity: number;
	// 盘点零售价
	takingSalePrice: number;
	// 盘点零售总价
	totalTakingSalePrice: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 有效期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 国家医保编码
	medicineCode: string;
	// 生产厂商
	manufacturerId: number;
	// 生产厂商名称
	manufacturerName: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
	// 药品类型
	drugType: string;
}