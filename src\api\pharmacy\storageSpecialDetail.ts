﻿import {useBaseApi} from '/@/api/base';

// 特殊处理明细表接口服务
export const useStorageSpecialDetailApi = () => {
	const baseApi = useBaseApi("storageSpecialDetail");
	return {
		// 分页查询特殊处理明细表
		page: baseApi.page,
		// 查看特殊处理明细表详细
		detail: baseApi.detail,
		// 新增特殊处理明细表
		add: baseApi.add,
		// 更新特殊处理明细表
		update: baseApi.update,
		// 删除特殊处理明细表
		delete: baseApi.delete,
		// 批量删除特殊处理明细表
		batchDelete: baseApi.batchDelete,
		// 导出特殊处理明细表数据
		exportData: baseApi.exportData,
		// 导入特殊处理明细表数据
		importData: baseApi.importData,
		// 下载特殊处理明细表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 特殊处理明细表实体
export interface StorageSpecialDetail {
	// 主键Id
	id: number;
	// 药品类型
	drugType: string;
	// 特殊处理单号
	handleNo: string;
	// 特殊处理ID
	handleId: number;
	// 药品ID
	drugId: number;
	// 库存ID
	inventoryId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 进价
	purchasePrice: number;
	// 零售价
	salePrice: number;
	// 总进价
	totalPurchasePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 数量
	quantity: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 有效期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 国家医保编码
	medicineCode: string;
	// 生产厂商
	manufacturerId: number;
	// 生产厂商名称
	manufacturerName: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}