/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemUnitOutput } from './charge-item-unit-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListChargeItemUnitOutput
 */
export interface SqlSugarPagedListChargeItemUnitOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ChargeItemUnitOutput>}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    items?: Array<ChargeItemUnitOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemUnitOutput
     */
    hasNextPage?: boolean;
}
