/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 排班时间段输出参数
 *
 * @export
 * @interface TimePeriodOutput
 */
export interface TimePeriodOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof TimePeriodOutput
     */
    id?: number;

    /**
     * 时间段编码
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    timePeriodCode?: string | null;

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    timePeriodName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    wubiCode?: string | null;

    /**
     * 开始时间
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    startTime?: string | null;

    /**
     * 结束时间
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    endTime?: string | null;

    /**
     * 时间段
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    timePeriodDetail?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof TimePeriodOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof TimePeriodOutput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    remark?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof TimePeriodOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof TimePeriodOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof TimePeriodOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof TimePeriodOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof TimePeriodOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof TimePeriodOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof TimePeriodOutput
     */
    tenantId?: number | null;
}
