/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddChargeItemUnitInput } from '../models';
import { AdminResultBoolean } from '../models';
import { AdminResultChargeItemUnit } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultSqlSugarPagedListChargeItemUnitOutput } from '../models';
import { DeleteChargeItemUnitInput } from '../models';
import { PageChargeItemUnitInput } from '../models';
import { SetChargeItemUnitStatusInput } from '../models';
import { UpdateChargeItemUnitInput } from '../models';
/**
 * ChargeItemUnitApi - axios parameter creator
 * @export
 */
export const ChargeItemUnitApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 添加收费项目单位数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitAddChargeItemUnitPost: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/addChargeItemUnit`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 增加收费项目单位 ➕
         * @param {AddChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitAddPost: async (body?: AddChargeItemUnitInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除收费项目单位 ❌
         * @param {Array<DeleteChargeItemUnitInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitBatchDeletePost: async (body: Array<DeleteChargeItemUnitInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiChargeItemUnitBatchDeletePost.');
            }
            const localVarPath = `/api/chargeItemUnit/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除收费项目单位 ❌
         * @param {DeleteChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitDeletePost: async (body?: DeleteChargeItemUnitInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取收费项目单位详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiChargeItemUnitDetailGet.');
            }
            const localVarPath = `/api/chargeItemUnit/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出收费项目单位记录 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitExportPost: async (body?: PageChargeItemUnitInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载收费项目单位数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入收费项目单位记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询收费项目单位 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitPagePost: async (body?: PageChargeItemUnitInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置收费项目单位状态 🚫
         * @param {SetChargeItemUnitStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitSetStatusPost: async (body?: SetChargeItemUnitStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新收费项目单位 ✏️
         * @param {UpdateChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUnitUpdatePost: async (body?: UpdateChargeItemUnitInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItemUnit/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ChargeItemUnitApi - functional programming interface
 * @export
 */
export const ChargeItemUnitApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 添加收费项目单位数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitAddChargeItemUnitPost(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultBoolean>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitAddChargeItemUnitPost(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 增加收费项目单位 ➕
         * @param {AddChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitAddPost(body?: AddChargeItemUnitInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除收费项目单位 ❌
         * @param {Array<DeleteChargeItemUnitInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitBatchDeletePost(body: Array<DeleteChargeItemUnitInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除收费项目单位 ❌
         * @param {DeleteChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitDeletePost(body?: DeleteChargeItemUnitInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取收费项目单位详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultChargeItemUnit>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出收费项目单位记录 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitExportPost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载收费项目单位数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入收费项目单位记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询收费项目单位 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitPagePost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemUnitOutput>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置收费项目单位状态 🚫
         * @param {SetChargeItemUnitStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitSetStatusPost(body?: SetChargeItemUnitStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新收费项目单位 ✏️
         * @param {UpdateChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitUpdatePost(body?: UpdateChargeItemUnitInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemUnitApiAxiosParamCreator(configuration).apiChargeItemUnitUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ChargeItemUnitApi - factory interface
 * @export
 */
export const ChargeItemUnitApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 添加收费项目单位数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitAddChargeItemUnitPost(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultBoolean>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitAddChargeItemUnitPost(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 增加收费项目单位 ➕
         * @param {AddChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitAddPost(body?: AddChargeItemUnitInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除收费项目单位 ❌
         * @param {Array<DeleteChargeItemUnitInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitBatchDeletePost(body: Array<DeleteChargeItemUnitInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除收费项目单位 ❌
         * @param {DeleteChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitDeletePost(body?: DeleteChargeItemUnitInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取收费项目单位详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultChargeItemUnit>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出收费项目单位记录 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitExportPost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载收费项目单位数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入收费项目单位记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询收费项目单位 🔖
         * @param {PageChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitPagePost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemUnitOutput>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置收费项目单位状态 🚫
         * @param {SetChargeItemUnitStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitSetStatusPost(body?: SetChargeItemUnitStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新收费项目单位 ✏️
         * @param {UpdateChargeItemUnitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUnitUpdatePost(body?: UpdateChargeItemUnitInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemUnitApiFp(configuration).apiChargeItemUnitUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ChargeItemUnitApi - object-oriented interface
 * @export
 * @class ChargeItemUnitApi
 * @extends {BaseAPI}
 */
export class ChargeItemUnitApi extends BaseAPI {
    /**
     * 
     * @summary 添加收费项目单位数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitAddChargeItemUnitPost(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultBoolean>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitAddChargeItemUnitPost(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 增加收费项目单位 ➕
     * @param {AddChargeItemUnitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitAddPost(body?: AddChargeItemUnitInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除收费项目单位 ❌
     * @param {Array<DeleteChargeItemUnitInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitBatchDeletePost(body: Array<DeleteChargeItemUnitInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除收费项目单位 ❌
     * @param {DeleteChargeItemUnitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitDeletePost(body?: DeleteChargeItemUnitInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取收费项目单位详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultChargeItemUnit>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出收费项目单位记录 🔖
     * @param {PageChargeItemUnitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitExportPost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载收费项目单位数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入收费项目单位记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询收费项目单位 🔖
     * @param {PageChargeItemUnitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitPagePost(body?: PageChargeItemUnitInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemUnitOutput>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置收费项目单位状态 🚫
     * @param {SetChargeItemUnitStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitSetStatusPost(body?: SetChargeItemUnitStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新收费项目单位 ✏️
     * @param {UpdateChargeItemUnitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemUnitApi
     */
    public async apiChargeItemUnitUpdatePost(body?: UpdateChargeItemUnitInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemUnitApiFp(this.configuration).apiChargeItemUnitUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
