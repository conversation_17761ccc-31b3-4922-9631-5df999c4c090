// Get medication route name
export function getMedicationRouteName(data: any[], rotueId: any): any {
	 
    if (data == null || rotueId == null) return '';
    var item = data.find((item: any) => item.id == rotueId);
    return item?.routeName?? rotueId;
}
export function getFrequencyName(data: any[], freqId: any): any {
	 
    if (data == null || freqId == null) return '';
    var item = data.find((item: any) => item.id == freqId);
    return item?.name?? freqId;
}
 