/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemPackOutput } from './charge-item-pack-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListChargeItemPackOutput
 */
export interface SqlSugarPagedListChargeItemPackOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ChargeItemPackOutput>}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    items?: Array<ChargeItemPackOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemPackOutput
     */
    hasNextPage?: boolean;
}
