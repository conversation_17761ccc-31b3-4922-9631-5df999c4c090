<template>
	<div class="appointment-create-container">
		<div class="form-container">
			<el-form :model="state.currentPatientInfo" ref="patientInfoRef" label-width="100" inline-message :show-message="false">
				<el-card class="full-table" shadow="hover" style="margin-top: 5px">
					<template #header>
						<div class="card-header">
							<el-tag class="ml-2">患者基本信息</el-tag>
						</div>
					</template>
					<el-row style="flex: 1">
						<el-form-item v-show="false">
							<el-input v-model="state.currentPatientInfo.id" />
						</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="门诊流水号" prop="visitNo">
								<el-input v-model="state.visitNoInput" placeholder="请输入门诊流水号" clearable @keyup.enter="queryVisitRecord" @clear="clearVisitData">
									<template #append>
										<el-button @click="queryVisitRecord" :loading="state.queryLoading">查询</el-button>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="卡号" prop="cardNo">
								<el-input v-model="state.currentPatientInfo.cardNo" disabled />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="门诊号" prop="outpatientNo">
								<el-input v-model="state.currentPatientInfo.outpatientNo" disabled />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="姓名" prop="patientName" required>
								<el-input v-model="state.currentPatientInfo.patientName" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="出生日期" prop="birthday" required>
								<el-date-picker v-model="state.currentPatientInfo.birthday" @change="calculateAgeAndUnit" type="date" placeholder="出生日期" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="年龄" prop="age" @input="calculateBirthDate" required>
								<el-input v-model="state.currentPatientInfo.age" type="number" placeholder="年龄">
									<template #append>
										<el-select v-model="state.currentPatientInfo.ageUnit" placeholder="岁" @change="calculateBirthDate" style="width: 52px">
											<el-option label="岁" value="0" />
											<el-option label="月" value="1" />
											<el-option label="天" value="2" />
										</el-select>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="性别" prop="sex" required>
								<g-sys-dict v-model="state.currentPatientInfo.sex" code="GenderEnum" render-as="select" placeholder="请选择性别" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="门诊结算类型" prop="feeId" disabled>
								<!-- <g-sys-dict v-model="state.currentPatientInfo.feeCategory" code="InpatientFeeCategory"
									render-as="select" placeholder="请选择费别" clearable filterable /> -->
								<el-select @change="feeCategoryChange" clearable filterable v-model="state.currentPatientInfo.feeId" placeholder="请选择结算类型">
									<el-option v-for="(item, index) in state.dropdownData.outpatientFeeCategoryData ?? []" :key="index" :value="item.id" :label="item.name" />
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="民族" prop="nation">
								<g-sys-dict v-model="state.currentPatientInfo.nation" code="NationEnum" render-as="select" placeholder="请选择民族" clearable filterable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="证件类型" prop="cardType" required>
								<g-sys-dict v-model="state.currentPatientInfo.cardType" code="CardTypeEnum" render-as="select" placeholder="请选择证件类型" clearable filterable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="证件号码" prop="idCardNo" required>
								<el-input v-model="state.currentPatientInfo.idCardNo" placeholder="请输入证件号码" clearable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="籍贯" prop="nativePlace">
								<el-cascader v-model="state.currentPatientInfo.nativePlace" :options="state.addressDicData" placeholder="请选择省市区县" clearable class="w100" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="婚姻状况" prop="marriage">
								<g-sys-dict v-model="state.currentPatientInfo.marriage" code="MaritalStatusEnum" render-as="select" placeholder="请选择婚姻状况" clearable filterable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="职业" prop="occupation">
								<g-sys-dict v-model="state.currentPatientInfo.occupation" code="Occupation" render-as="select" placeholder="请选择职业" clearable filterable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="联系人" prop="contactName">
								<el-input v-model="state.currentPatientInfo.contactName" placeholder="联系人" clearable>
									<template #append>
										<el-select v-model="state.currentPatientInfo.contactRelationship" placeholder="关系" @change="calculateBirthDate" style="width: 80px">
											<el-option label="同学同事" value="09" />
											<el-option label="本人" value="01" />
											<el-option label="配偶" value="02" />
											<el-option label="子" value="03" />
											<el-option label="女" value="04" />
											<el-option label="父母" value="06" />
											<el-option label="孙辈" value="05" />
											<el-option label="祖辈" value="07" />
											<el-option label="其他" value="10" />
											<el-option label="兄弟姐妹" value="08" />
										</el-select>
									</template>
								</el-input>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="联系人地区" prop="contactAddress">
								<el-cascader v-model="state.currentPatientInfo.contactAddress" :options="state.addressDicData" placeholder="请选择省市区县" clearable class="w100" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="联系人地址" prop="contactAddressStreet">
								<el-input v-model="state.currentPatientInfo.contactAddressStreet" placeholder="" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="联系人电话" prop="contactPhone">
								<el-input v-model="state.currentPatientInfo.contactPhone" placeholder="请输入联系人电话" type="phone" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="国籍" prop="nationality">
								<g-sys-dict v-model="state.currentPatientInfo.nationality" code="Nationality" render-as="select" placeholder="请选择国籍" clearable filterable />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="患者电话" prop="phone">
								<el-input v-model="state.currentPatientInfo.phone" placeholder="" type="phone" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="户口" prop="householdRegistrationAddress">
								<el-cascader v-model="state.currentPatientInfo.householdRegistrationAddress" :options="state.addressDicData" placeholder="请选择省市区县" clearable class="w100" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="户口地址" prop="householdRegistrationAddressStreet">
								<el-input v-model="state.currentPatientInfo.householdRegistrationAddressStreet" placeholder="" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="户口邮编" prop="householdRegistrationPostcode">
								<el-input v-model="state.currentPatientInfo.householdRegistrationPostcode" placeholder="" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="现居住地区" prop="residence">
								<el-cascader v-model="state.currentPatientInfo.residence" :options="state.addressDicData" placeholder="请选择省市区县" clearable class="w100" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="现居住地址" prop="residenceAddress">
								<el-input v-model="state.currentPatientInfo.residenceAddressStreet" placeholder="输入详细地址" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="医保卡" prop="hasMedicalInsurance">
								<el-switch v-model="state.currentPatientInfo.hasMedicalInsurance" active-text="有" inactive-text="无" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-card>
			</el-form>

			<el-card class="full-table" shadow="hover" style="margin-top: 5px" v-loading="state.loading">
				<template #header>
					<div class="card-header">
						<el-tag class="ml-2">预约信息</el-tag>
					</div>
				</template>
				<el-form :model="state.appointmentInfo" ref="appointmentInfoRef" label-width="100" inline-message :show-message="false">
					<el-row style="flex: 1">
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="主要诊断" prop="diagnosticCode">
								<el-select
									clearable
									filterable
									v-model="state.appointmentInfo.diagnosticCode"
									remote
									reserve-keyword
									:remote-method="diagnosticSearchMethod"
									:loading="state.loading"
									@change="diagnosticChange"
									placeholder="请选择诊断"
								>
									<el-option
										v-if="state.diagnosticOptionData.length === 0 && state.appointmentInfo.diagnosticCode"
										:value="state.appointmentInfo.diagnosticCode"
										:key="state.appointmentInfo.diagnosticCode"
										:label="state.appointmentInfo.diagnosticName"
									>
									</el-option>
									<el-option v-else v-for="item in state.diagnosticOptionData" :label="item.name" :key="item.code" :value="item.code" />
									<!-- <el-option
                                        v-if="state.diagnosticOptionData.length === 0 && state.appointmentInfo.diagnosticCode"
                                        :value="state.appointmentInfo.diagnosticCode"
                                        :label="state.appointmentInfo.diagnosticName">
                                    </el-option>
                                    <el-option v-for="item in state.diagnosticOptionData" :key="item.code"
                                        :label="item.name" :value="item.code" /> -->
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="住院费别" prop="feeId" required>
								<el-select @change="feeCategoryChange" clearable filterable v-model="state.appointmentInfo.feeId" placeholder="请选择费别">
									<el-option v-for="(item, index) in state.dropdownData.inpatientFeeCategoryData ?? []" :key="index" :value="item.id" :label="item.name" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="接诊医生" prop="doctorId" required>
								<el-select clearable filterable v-model="state.appointmentInfo.doctorId" placeholder="请选择医生">
									<el-option v-for="(item, index) in state.dropdownData.doctors ?? []" :key="index" :value="item.value" :label="item.label" />
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="住院科室" prop="deptId" required>
								<el-select clearable filterable v-model="state.appointmentInfo.deptId" placeholder="请选择科室">
									<el-option v-for="(item, index) in state.dropdownData.depts ?? []" :key="index" :value="item.value" :label="item.label" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="剩余床位" prop="residenceAddress">
								<el-input v-model="state.appointmentInfo.residenceAddressStreet" disabled />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" class="mb5">
							<el-form-item label="预约日期" prop="appointmentTime">
								<el-date-picker v-model="state.appointmentInfo.appointmentTime" type="datetime" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="16" :lg="12" :xl="12" class="mb5">
							<el-form-item label="备注" prop="remark">
								<el-input v-model="state.appointmentInfo.remark" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-card>
		</div>

		<el-row class="foot">
			<el-col :span="24">
				<el-card style="margin-top: 5px" :body-style="{ padding: '0px' }" shadow="never">
					<div class="button-container">
						<el-button-group>
							<el-button type="info" icon="ele-Refresh" @click="initData">刷新</el-button>
							<el-button type="primary" :disabled="state.appointmentInfo.id == 0 || state.appointmentInfo.id == null" @click="delApointmentRecord" icon="ele-Delete" v-reclick="5000">删除</el-button>

							<!-- <el-button type="warning"
                                :disabled="state.appointmentInfo.id == 0 && state.appointmentInfo.id == null"
                                icon="ele-Finished" v-reclick="5000">修改</el-button> -->

							<el-button type="primary" icon="ele-UploadFilled" @click="submit" v-reclick="5000">保存</el-button>

							<el-button type="info" icon="ele-Printer" v-reclick="5000">打印</el-button>
							<el-button type="info" icon="ele-View">打印预览</el-button>
						</el-button-group>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>
<script setup lang="ts" name="appointment-create">
import { ref, reactive, watch, onMounted } from 'vue';
import { ElMessageBox, ElMessage, ElTable } from 'element-plus';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
import { RegisterOutput } from '/@/api-modules/outpatientDoctor/models';
import { SysRegionApi } from '/@/api-services/api';
import { getAPI } from '/@/utils/axios-utils';
import { useIcd10Api } from '/@/api/shared/icd10';
import { useAppointmentRecordApi } from '/@/api/inpatient/appointmentRecord';
import { useVisitApi } from '/@/api/outpatientDoctor/visit';
import { useInpatientRegisterApi } from '/@/api/inpatient/register/inpatientRegister';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
const basicInfoApi = useBasicInfoApi();
const inpatientRegisterApi = useInpatientRegisterApi();
const visitApi = useVisitApi();
const icd10Api = useIcd10Api();
const appointmentRecordApi = useAppointmentRecordApi();
const appointmentInfoRef = ref();
const state = reactive({
	currentPatientInfo: {} as any, //当前病人信息
	visitNoInput: '', // 门诊流水号输入
	queryLoading: false, // 查询加载状态

	addressDicData: [] as any[],
	dropdownData: {} as any,
	loading: false,
	diagnosticOptionData: [] as any[], //  诊断检索到的数据

	appointmentInfo: {
		diagnosticCode: '',
		diagnosticName: '',
	} as any, // 预约信息
});

//父级传递来的参数
var props = defineProps({
	patientInfo: {
		type: Object as () => RegisterOutput, // 使用 Object 和工厂函数指定类型
		required: true,
	},
});
watch(
	() => props.patientInfo,
	async (val: any) => {
		console.log('open patientInfo', props.patientInfo, val);
		state.currentPatientInfo = props.patientInfo;
		state.currentPatientInfo.nation = Number(state.currentPatientInfo.nation);

		// 出生地
		state.currentPatientInfo.birthplace = [String(state.currentPatientInfo.birthplaceProvince), String(state.currentPatientInfo.birthplaceCity), String(state.currentPatientInfo.birthplaceCounty)];

		// 现居住地
		state.currentPatientInfo.residence = [String(state.currentPatientInfo.residenceProvince), String(state.currentPatientInfo.residenceCity), String(state.currentPatientInfo.residenceCounty)];

		// 工作地址
		state.currentPatientInfo.workAddress = [String(state.currentPatientInfo.workProvince), String(state.currentPatientInfo.workCity), String(state.currentPatientInfo.workCounty)];
		// 籍贯
		state.currentPatientInfo.nativePlace = [String(state.currentPatientInfo.nativePlaceProvince), String(state.currentPatientInfo.nativePlaceCity), String(state.currentPatientInfo.nativePlaceCounty)];

		initData();
	}
);
const feeCategoryChange = (val: any) => {};
const diagnosticChange = (code: string) => {
	state.appointmentInfo.diagnosticCode = code;
	state.appointmentInfo.diagnosticName = state.diagnosticOptionData.filter((item) => item.code === code)[0]?.name;
};

const diagnosticSearchMethod = async (query: string) => {
	if (query && query.length > 1) {
		const res = await icd10Api
			.page({ pageSize: 20, keyword: query })
			.then((res) => {
				state.diagnosticOptionData = res.data.result?.items ?? [];
			})
			.catch((err) => {
				state.diagnosticOptionData = [];
			});
	} else {
		state.diagnosticOptionData = [];
	}
};

// 页面加载时
onMounted(async () => {
	console.log('onMounted');
});
const initData = async () => {
	state.loading = true;
	let addressDicData = await getAPI(SysRegionApi).apiSysRegionTreeGet();
	state.addressDicData = addressDicData.data.result ?? [];

	basicInfoApi.getUsers({}).then((res: any) => {
		state.dropdownData.doctors = res.data.result;
	});
	basicInfoApi.getDepartments({}).then((res: any) => {
		state.dropdownData.depts = res.data.result;
	});
	//费别
	// 获取费别数据并进行筛选
	const feeCategoryResponse = await basicInfoApi.getFeeCategories({ pageSize: 100, status: 1, usageScope: 2 });
	state.dropdownData.inpatientFeeCategoryData = feeCategoryResponse.data.result?.filter((item: any) => item.usageScope === 2) ?? [];
	state.dropdownData.outpatientFeeCategoryData = feeCategoryResponse.data.result?.filter((item: any) => item.usageScope === 1) ?? [];

	if (state.currentPatientInfo.visitNo) {
		await appointmentRecordApi
			.page({ pageSize: 100, visitNo: state.currentPatientInfo.visitNo })
			.then((res) => {
				state.appointmentInfo = res.data.result?.items[0] ?? {};

				if (!state.appointmentInfo.diagnosticCode) {
					state.appointmentInfo.diagnosticName = state.currentPatientInfo.diagnosticName;
					state.appointmentInfo.diagnosticCode = state.currentPatientInfo.diagnosticCode;
					state.appointmentInfo.appointmentTime = new Date();
				}
				state.loading = false;
			})
			.catch((err) => {
				state.loading = false;
			});
	} else {
		state.loading = false;
	}
};

// 提交
const submit = async () => {
	appointmentInfoRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.appointmentInfo;
			values.patientId = state.currentPatientInfo.patientId;
			values.patientName = state.currentPatientInfo.patientName;
			values.medicalCardId = state.currentPatientInfo.cardId;
			values.medicalCardNo = state.currentPatientInfo.cardNo;
			values.outpatientNo = state.currentPatientInfo.outpatientNo;
			values.birthday = state.currentPatientInfo.birthday;
			values.visitNo = state.currentPatientInfo.visitNo;
			values.outpatientRegisterId = state.currentPatientInfo.registerId;
			values.outpatientVisitId = state.currentPatientInfo.visitId;
			values.idCardType = state.currentPatientInfo.cardType;
			values.idCardNo = state.currentPatientInfo.idCardNo;
			values.inpatientWay = '1'; // 门诊预约
			// 医生姓名
			values.doctorName = state.dropdownData.doctors.find((item: any) => item.value === values.doctorId)?.label ?? '';
			// 科室名称
			values.deptName = state.dropdownData.depts.find((item: any) => item.value === values.deptId)?.label ?? '';
			console.log('提交数据', state.appointmentInfo);
			await appointmentRecordApi[state.appointmentInfo.id ? 'update' : 'outpatientAppointment'](values)
				.then(async (res: any) => {
					await appointmentRecordApi.detail(res.data.result).then((res) => {
						state.appointmentInfo = res.data.result ?? {};
					});
					state.loading = false;
					ElMessage.success(`操作成功`);
				})
				.catch((err) => {
					state.loading = false;
				});
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};
// 批量删除
const delApointmentRecord = () => {
	ElMessageBox.confirm(`确定要删除门诊预约记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await appointmentRecordApi.delete({ id: state.appointmentInfo.id }).then((res) => {
				ElMessage.success(`成功批量删除`);
				initData();
			});
		})
		.catch(() => {});
};

const calculateAgeAndUnit = () => {
	if (!state.currentPatientInfo.birthday) return;

	const birthDate = new Date(state.currentPatientInfo.birthday);
	const today = new Date();

	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();

	if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
		age--;
	}

	if (age < 1) {
		let months = (today.getFullYear() - birthDate.getFullYear()) * 12 + (today.getMonth() - birthDate.getMonth());
		if (today.getDate() < birthDate.getDate()) {
			months--;
		}

		if (months < 1) {
			const timeDiff = today.getTime() - birthDate.getTime();
			const days = Math.floor(timeDiff / (1000 * 3600 * 24));
			state.currentPatientInfo.age = days;
			state.currentPatientInfo.ageUnit = '2'; // 天
		} else {
			state.currentPatientInfo.age = months;
			state.currentPatientInfo.ageUnit = '1'; // 月
		}
	} else {
		state.currentPatientInfo.age = age;
		state.currentPatientInfo.ageUnit = '0'; // 岁
	}
};

const calculateBirthDate = () => {
	if (!state.currentPatientInfo.age || isNaN(state.currentPatientInfo.age)) return;

	const today = new Date();
	let birthDate;

	switch (state.currentPatientInfo.ageUnit) {
		case '0': // 岁
			birthDate = new Date(today.getFullYear() - state.currentPatientInfo.age, today.getMonth(), today.getDate());
			break;
		case '1': // 月
			birthDate = new Date(today.getFullYear(), today.getMonth() - state.currentPatientInfo.age, today.getDate());
			break;
		case '2': // 天
			birthDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - state.currentPatientInfo.age);
			break;
		default:
			break;
	}

	state.currentPatientInfo.birthday = birthDate;
};

// 查询门诊记录
const queryVisitRecord = async () => {
	if (!state.visitNoInput || state.visitNoInput.trim() === '') {
		ElMessage.warning('请输入门诊流水号');
		return;
	}

	state.queryLoading = true;
	try {
		const response = await visitApi.getVisit(state.visitNoInput.trim());
		if (response.data && response.data.result) {
			fillPatientInfo(response.data.result);
			ElMessage.success('门诊记录查询成功');
		} else {
			ElMessage.warning('未找到对应的门诊记录');
		}
	} catch (error) {
		console.error('查询门诊记录失败:', error);
	} finally {
		state.queryLoading = false;
	}
};

// 清空门诊数据
const clearVisitData = () => {
	state.visitNoInput = '';
	// 可以选择是否清空已填充的患者信息
	// state.currentPatientInfo = {};
};

// 填充患者信息
const fillPatientInfo = (visitData: any) => {
	// 基本信息映射
	state.currentPatientInfo = {
		...state.currentPatientInfo,
		id: visitData.id,
		patientId: visitData.patientId,
		patientName: visitData.patientName,
		cardNo: visitData.cardNo,
		outpatientNo: visitData.outpatientNo,
		visitNo: visitData.visitNo,
		registerId: visitData.registerId || visitData.id,
		visitId: visitData.visitId || visitData.id,
		cardId: visitData.cardId,

		// 个人信息
		sex: visitData.sex,
		age: visitData.age,
		ageUnit: visitData.ageUnit || '0',
		birthday: visitData.birthday ? new Date(visitData.birthday) : null,
		cardType: visitData.cardType,
		idCardNo: visitData.idCardNo,
		nation: visitData.nation,
		marriage: visitData.marriage,
		occupation: visitData.occupation,
		nationality: visitData.nationality,
		phone: visitData.phone,

		// 联系人信息
		contactName: visitData.contactName,
		contactRelationship: visitData.contactRelationship,
		contactPhone: visitData.contactPhone,

		// 地址信息 - 处理级联选择器的数据格式
		nativePlace:
			visitData.nativePlaceProvince && visitData.nativePlaceCity && visitData.nativePlaceCounty
				? [String(visitData.nativePlaceProvince), String(visitData.nativePlaceCity), String(visitData.nativePlaceCounty)]
				: [],
		contactAddress:
			visitData.contactAddressProvince && visitData.contactAddressCity && visitData.contactAddressCounty
				? [String(visitData.contactAddressProvince), String(visitData.contactAddressCity), String(visitData.contactAddressCounty)]
				: [],
		contactAddressStreet: visitData.contactAddressStreet,
		householdRegistrationAddress:
			visitData.householdRegistrationProvince && visitData.householdRegistrationCity && visitData.householdRegistrationCounty
				? [String(visitData.householdRegistrationProvince), String(visitData.householdRegistrationCity), String(visitData.householdRegistrationCounty)]
				: [],
		householdRegistrationAddressStreet: visitData.householdRegistrationAddressStreet,
		householdRegistrationPostcode: visitData.householdRegistrationPostcode,
		residence:
			visitData.residenceProvince && visitData.residenceCity && visitData.residenceCounty
				? [String(visitData.residenceProvince), String(visitData.residenceCity), String(visitData.residenceCounty)]
				: [],
		residenceAddressStreet: visitData.residenceAddressStreet,

		// 费别和医保信息
		feeId: visitData.feeId,
		hasMedicalInsurance: visitData.hasMedicalInsurance || false,

		// 诊断信息
		diagnosticCode: visitData.diagnosticCode,
		diagnosticName: visitData.diagnosticName,
	};

	// 如果有诊断信息，同时填充到预约信息中
	if (visitData.diagnosticCode || visitData.diagnosticName) {
		state.appointmentInfo.diagnosticCode = visitData.diagnosticCode;
		state.appointmentInfo.diagnosticName = visitData.diagnosticName;
	}

	// 触发年龄计算（如果有出生日期）
	if (state.currentPatientInfo.birthday) {
		calculateAgeAndUnit();
	}
};
</script>
<style scoped>
.inpatientAppointmentApplication-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	/* 使容器占满整个视口高度 */
}

.form-container {
	flex: 1;
	/* 表单容器占据剩余空间 */
	overflow-y: auto;
	/* 增加垂直滚动条 */
	padding: 0px;
	/* 可选：添加内边距 */
}

.foot {
	position: sticky;
	bottom: 0;
	background-color: white;
	/* 背景色防止内容穿透 */
	z-index: 1000;
	/* 确保底部内容在最上层 */
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

:deep(.el-card__header) {
	padding: 5px;
}

:deep(.el-card__body) {
	padding: 10px;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	padding: 10px;
	/* 可选：添加一些内边距 */
}

.el-container {
	display: flex;
	flex-direction: column;
}

.el-footer {
	background: white;
	padding: 16px;
	box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
}
</style>
