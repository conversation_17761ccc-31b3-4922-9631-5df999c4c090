/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 挂号类别输出参数
 *
 * @export
 * @interface RegCategoryDto
 */
export interface RegCategoryDto {

    /**
     * 收费项目
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    chargeItemIdFkColumn?: string | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    wubiCode?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    consultationFee?: number | null;

    /**
     * 收费项目
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    chargeItemId?: number | null;

    /**
     * 状态
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    status?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    remark?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof RegCategoryDto
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof RegCategoryDto
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof RegCategoryDto
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof RegCategoryDto
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof RegCategoryDto
     */
    tenantId?: number | null;
}
