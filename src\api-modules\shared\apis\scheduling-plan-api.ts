/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddSchedulingPlanInput } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListSchedulingPlanOutput } from '../models';
import { AdminResultSqlSugarPagedListSchedulingPlanOutput } from '../models';
import { DeleteSchedulingPlanInput } from '../models';
import { SchedulingPlanInput } from '../models';
import { UpdateSchedulingPlanInput } from '../models';
/**
 * SchedulingPlanApi - axios parameter creator
 * @export
 */
export const SchedulingPlanApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加排班计划
         * @param {AddSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchedulingPlanAddPost: async (body?: AddSchedulingPlanInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedulingPlan/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除排班计划
         * @param {DeleteSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchedulingPlanDeletePost: async (body?: DeleteSchedulingPlanInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedulingPlan/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据门诊日期获取排班计划列表
         * @param {Date} [outpatientDate] 门诊日期
         * @param {number} [deptId] 科室id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchedulingPlanListGet: async (outpatientDate?: Date, deptId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedulingPlan/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (outpatientDate !== undefined) {
                localVarQueryParameter['outpatientDate'] = (outpatientDate as any instanceof Date) ?
                    (outpatientDate as any).toISOString() :
                    outpatientDate;
            }

            if (deptId !== undefined) {
                localVarQueryParameter['deptId'] = deptId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询排班计划
         * @param {SchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchedulingPlanPagePost: async (body?: SchedulingPlanInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedulingPlan/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新排班计划
         * @param {UpdateSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchedulingPlanUpdatePost: async (body?: UpdateSchedulingPlanInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schedulingPlan/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SchedulingPlanApi - functional programming interface
 * @export
 */
export const SchedulingPlanApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加排班计划
         * @param {AddSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanAddPost(body?: AddSchedulingPlanInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await SchedulingPlanApiAxiosParamCreator(configuration).apiSchedulingPlanAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除排班计划
         * @param {DeleteSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanDeletePost(body?: DeleteSchedulingPlanInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SchedulingPlanApiAxiosParamCreator(configuration).apiSchedulingPlanDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据门诊日期获取排班计划列表
         * @param {Date} [outpatientDate] 门诊日期
         * @param {number} [deptId] 科室id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanListGet(outpatientDate?: Date, deptId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListSchedulingPlanOutput>>> {
            const localVarAxiosArgs = await SchedulingPlanApiAxiosParamCreator(configuration).apiSchedulingPlanListGet(outpatientDate, deptId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询排班计划
         * @param {SchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanPagePost(body?: SchedulingPlanInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListSchedulingPlanOutput>>> {
            const localVarAxiosArgs = await SchedulingPlanApiAxiosParamCreator(configuration).apiSchedulingPlanPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新排班计划
         * @param {UpdateSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanUpdatePost(body?: UpdateSchedulingPlanInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SchedulingPlanApiAxiosParamCreator(configuration).apiSchedulingPlanUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SchedulingPlanApi - factory interface
 * @export
 */
export const SchedulingPlanApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加排班计划
         * @param {AddSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanAddPost(body?: AddSchedulingPlanInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return SchedulingPlanApiFp(configuration).apiSchedulingPlanAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除排班计划
         * @param {DeleteSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanDeletePost(body?: DeleteSchedulingPlanInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SchedulingPlanApiFp(configuration).apiSchedulingPlanDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据门诊日期获取排班计划列表
         * @param {Date} [outpatientDate] 门诊日期
         * @param {number} [deptId] 科室id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanListGet(outpatientDate?: Date, deptId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListSchedulingPlanOutput>> {
            return SchedulingPlanApiFp(configuration).apiSchedulingPlanListGet(outpatientDate, deptId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询排班计划
         * @param {SchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanPagePost(body?: SchedulingPlanInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListSchedulingPlanOutput>> {
            return SchedulingPlanApiFp(configuration).apiSchedulingPlanPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新排班计划
         * @param {UpdateSchedulingPlanInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchedulingPlanUpdatePost(body?: UpdateSchedulingPlanInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SchedulingPlanApiFp(configuration).apiSchedulingPlanUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SchedulingPlanApi - object-oriented interface
 * @export
 * @class SchedulingPlanApi
 * @extends {BaseAPI}
 */
export class SchedulingPlanApi extends BaseAPI {
    /**
     * 
     * @summary 增加排班计划
     * @param {AddSchedulingPlanInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchedulingPlanApi
     */
    public async apiSchedulingPlanAddPost(body?: AddSchedulingPlanInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return SchedulingPlanApiFp(this.configuration).apiSchedulingPlanAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除排班计划
     * @param {DeleteSchedulingPlanInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchedulingPlanApi
     */
    public async apiSchedulingPlanDeletePost(body?: DeleteSchedulingPlanInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SchedulingPlanApiFp(this.configuration).apiSchedulingPlanDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据门诊日期获取排班计划列表
     * @param {Date} [outpatientDate] 门诊日期
     * @param {number} [deptId] 科室id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchedulingPlanApi
     */
    public async apiSchedulingPlanListGet(outpatientDate?: Date, deptId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListSchedulingPlanOutput>> {
        return SchedulingPlanApiFp(this.configuration).apiSchedulingPlanListGet(outpatientDate, deptId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询排班计划
     * @param {SchedulingPlanInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchedulingPlanApi
     */
    public async apiSchedulingPlanPagePost(body?: SchedulingPlanInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListSchedulingPlanOutput>> {
        return SchedulingPlanApiFp(this.configuration).apiSchedulingPlanPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新排班计划
     * @param {UpdateSchedulingPlanInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchedulingPlanApi
     */
    public async apiSchedulingPlanUpdatePost(body?: UpdateSchedulingPlanInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SchedulingPlanApiFp(this.configuration).apiSchedulingPlanUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
