/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeCategory } from './charge-category';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultListChargeCategory
 */
export interface AdminResultListChargeCategory {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultListChargeCategory
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultListChargeCategory
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultListChargeCategory
     */
    message?: string | null;

    /**
     * 数据
     *
     * @type {Array<ChargeCategory>}
     * @memberof AdminResultListChargeCategory
     */
    result?: Array<ChargeCategory> | null;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultListChargeCategory
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultListChargeCategory
     */
    time?: Date;
}
