/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费类别增加输入参数
 *
 * @export
 * @interface AddChargeCategoryInput
 */
export interface AddChargeCategoryInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddChargeCategoryInput
     */
    name: string;

    /**
     * 提成
     *
     * @type {number}
     * @memberof AddChargeCategoryInput
     */
    commission?: number | null;

    /**
     * 记账属性
     *
     * @type {number}
     * @memberof AddChargeCategoryInput
     */
    accountAttribute: number;

    /**
     * 类型
     *
     * @type {number}
     * @memberof AddChargeCategoryInput
     */
    type: number;

    /**
     * 医保类型
     *
     * @type {string}
     * @memberof AddChargeCategoryInput
     */
    medInsType: string;

    /**
     * @type {StatusEnum}
     * @memberof AddChargeCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddChargeCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddChargeCategoryInput
     */
    remark?: string | null;
}
