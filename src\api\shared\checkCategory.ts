﻿import { useBaseApi } from '/@/api/base';

// 检查类别接口服务
export const useCheckCategoryApi = () => {
	const baseApi = useBaseApi('checkCategory');
	return {
		// 分页查询检查类别
		page: baseApi.page,
		// 查询检查类别列表
		list: function (cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'list',
					method: 'get',
				},
				cancel
			);
		},
		// 查看检查类别详细
		detail: baseApi.detail,
		// 新增检查类别
		add: baseApi.add,
		// 更新检查类别
		update: baseApi.update,
		// 设置检查类别状态
		setStatus: baseApi.setStatus,
		// 删除检查类别
		delete: baseApi.delete,
		// 批量删除检查类别
		batchDelete: baseApi.batchDelete,
		// 导出检查类别数据
		exportData: baseApi.exportData,
		// 导入检查类别数据
		importData: baseApi.importData,
		// 下载检查类别数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	};
};

// 检查类别实体
export interface CheckCategory {
	// 主键Id
	id: number;
	// 编码
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 收费类别
	chargeCategoryId?: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}
