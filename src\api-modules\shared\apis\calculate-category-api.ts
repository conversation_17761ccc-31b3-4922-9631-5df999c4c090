/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddCalculateCategoryInput } from '../models';
import { AdminResultCalculateCategory } from '../models';
import { AdminResultListCalculateCategory } from '../models';
import { AdminResultSqlSugarPagedListCalculateCategory } from '../models';
import { DeleteCalculateCategoryInput } from '../models';
import { PageCalculateCategoryInput } from '../models';
import { SetStatusCalculateCategoryInput } from '../models';
import { UpdateCalculateCategoryInput } from '../models';
/**
 * CalculateCategoryApi - axios parameter creator
 * @export
 */
export const CalculateCategoryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加核算类别
         * @param {AddCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryAddPost: async (body?: AddCalculateCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除核算类别
         * @param {DeleteCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryDeletePost: async (body?: DeleteCalculateCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取核算类别
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiCalculateCategoryDetailGet.');
            }
            const localVarPath = `/api/calculateCategory/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取核算类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询核算类别
         * @param {PageCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryPagePost: async (body?: PageCalculateCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategorySetStatusPost: async (body?: SetStatusCalculateCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新核算类别
         * @param {UpdateCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCalculateCategoryUpdatePost: async (body?: UpdateCalculateCategoryInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/calculateCategory/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CalculateCategoryApi - functional programming interface
 * @export
 */
export const CalculateCategoryApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加核算类别
         * @param {AddCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryAddPost(body?: AddCalculateCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除核算类别
         * @param {DeleteCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryDeletePost(body?: DeleteCalculateCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取核算类别
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultCalculateCategory>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取核算类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListCalculateCategory>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询核算类别
         * @param {PageCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryPagePost(body?: PageCalculateCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListCalculateCategory>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategorySetStatusPost(body?: SetStatusCalculateCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategorySetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新核算类别
         * @param {UpdateCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryUpdatePost(body?: UpdateCalculateCategoryInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CalculateCategoryApiAxiosParamCreator(configuration).apiCalculateCategoryUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * CalculateCategoryApi - factory interface
 * @export
 */
export const CalculateCategoryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加核算类别
         * @param {AddCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryAddPost(body?: AddCalculateCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除核算类别
         * @param {DeleteCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryDeletePost(body?: DeleteCalculateCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取核算类别
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultCalculateCategory>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取核算类别列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListCalculateCategory>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询核算类别
         * @param {PageCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryPagePost(body?: PageCalculateCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListCalculateCategory>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategorySetStatusPost(body?: SetStatusCalculateCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategorySetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新核算类别
         * @param {UpdateCalculateCategoryInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCalculateCategoryUpdatePost(body?: UpdateCalculateCategoryInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CalculateCategoryApiFp(configuration).apiCalculateCategoryUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CalculateCategoryApi - object-oriented interface
 * @export
 * @class CalculateCategoryApi
 * @extends {BaseAPI}
 */
export class CalculateCategoryApi extends BaseAPI {
    /**
     * 
     * @summary 增加核算类别
     * @param {AddCalculateCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryAddPost(body?: AddCalculateCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除核算类别
     * @param {DeleteCalculateCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryDeletePost(body?: DeleteCalculateCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取核算类别
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultCalculateCategory>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取核算类别列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListCalculateCategory>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询核算类别
     * @param {PageCalculateCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryPagePost(body?: PageCalculateCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListCalculateCategory>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置状态
     * @param {SetStatusCalculateCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategorySetStatusPost(body?: SetStatusCalculateCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategorySetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新核算类别
     * @param {UpdateCalculateCategoryInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CalculateCategoryApi
     */
    public async apiCalculateCategoryUpdatePost(body?: UpdateCalculateCategoryInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CalculateCategoryApiFp(this.configuration).apiCalculateCategoryUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
