/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费类别更新输入参数
 *
 * @export
 * @interface UpdateChargeCategoryInput
 */
export interface UpdateChargeCategoryInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateChargeCategoryInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateChargeCategoryInput
     */
    name: string;

    /**
     * 提成
     *
     * @type {number}
     * @memberof UpdateChargeCategoryInput
     */
    commission?: number | null;

    /**
     * 记账属性
     *
     * @type {number}
     * @memberof UpdateChargeCategoryInput
     */
    accountAttribute: number;

    /**
     * 类型
     *
     * @type {number}
     * @memberof UpdateChargeCategoryInput
     */
    type: number;

    /**
     * 医保类型
     *
     * @type {string}
     * @memberof UpdateChargeCategoryInput
     */
    medInsType: string;

    /**
     * @type {StatusEnum}
     * @memberof UpdateChargeCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateChargeCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateChargeCategoryInput
     */
    remark?: string | null;
}
