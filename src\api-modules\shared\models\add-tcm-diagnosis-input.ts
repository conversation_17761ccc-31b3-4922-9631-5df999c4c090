/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医诊断增加输入参数
 *
 * @export
 * @interface AddTcmDiagnosisInput
 */
export interface AddTcmDiagnosisInput {

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof AddTcmDiagnosisInput
     */
    tcmDiagnosisCode: string;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof AddTcmDiagnosisInput
     */
    tcmDiagnosisName: string;

    /**
     * 版本
     *
     * @type {string}
     * @memberof AddTcmDiagnosisInput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddTcmDiagnosisInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddTcmDiagnosisInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddTcmDiagnosisInput
     */
    orderNo?: number | null;
}
