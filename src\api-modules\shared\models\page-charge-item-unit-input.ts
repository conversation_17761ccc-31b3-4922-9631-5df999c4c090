/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 收费项目单位分页查询输入参数
 *
 * @export
 * @interface PageChargeItemUnitInput
 */
export interface PageChargeItemUnitInput {

    /**
     * @type {Search}
     * @memberof PageChargeItemUnitInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageChargeItemUnitInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageChargeItemUnitInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageChargeItemUnitInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    descStr?: string | null;

    /**
     * 单位编码
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    unitCode?: string | null;

    /**
     * 单位名称
     *
     * @type {string}
     * @memberof PageChargeItemUnitInput
     */
    unitName?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageChargeItemUnitInput
     */
    selectKeyList?: Array<number> | null;
}
