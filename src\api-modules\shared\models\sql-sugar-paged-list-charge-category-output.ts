/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeCategoryOutput } from './charge-category-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListChargeCategoryOutput
 */
export interface SqlSugarPagedListChargeCategoryOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ChargeCategoryOutput>}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    items?: Array<ChargeCategoryOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeCategoryOutput
     */
    hasNextPage?: boolean;
}
