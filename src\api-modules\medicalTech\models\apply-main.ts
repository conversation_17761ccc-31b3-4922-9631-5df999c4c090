/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单主表
 *
 * @export
 * @interface ApplyMain
 */
export interface ApplyMain {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ApplyMain
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ApplyMain
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ApplyMain
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    tenantId?: number | null;

    /**
     * 申请单号
     *
     * @type {string}
     * @memberof ApplyMain
     */
    applyNo: string;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    registerId: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof ApplyMain
     */
    visitNo: string;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    patientId: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof ApplyMain
     */
    itemCode?: string | null;

    /**
     * 项目名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ApplyMain
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ApplyMain
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ApplyMain
     */
    quantity?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ApplyMain
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ApplyMain
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ApplyMain
     */
    price?: number | null;

    /**
     * 总金额
     *
     * @type {number}
     * @memberof ApplyMain
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof ApplyMain
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof ApplyMain
     */
    flag?: number | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof ApplyMain
     */
    urgencyLevel?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof ApplyMain
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof ApplyMain
     */
    prescriptionType?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    checkCategoryName?: string | null;

    /**
     * 检查部位id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    checkPointId?: number | null;

    /**
     * 检查部位名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    checkPointName?: string | null;

    /**
     * 检查目的
     *
     * @type {string}
     * @memberof ApplyMain
     */
    checkObjective?: string | null;

    /**
     * 症状
     *
     * @type {string}
     * @memberof ApplyMain
     */
    symptom?: string | null;

    /**
     * 药物过敏
     *
     * @type {string}
     * @memberof ApplyMain
     */
    drugAllergy?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof ApplyMain
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    diagnosticName?: string | null;

    /**
     * 其他诊断编码
     *
     * @type {string}
     * @memberof ApplyMain
     */
    otherDiagnosticCode?: string | null;

    /**
     * 其他诊断名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    otherDiagnosticName?: string | null;

    /**
     * 病情介绍
     *
     * @type {string}
     * @memberof ApplyMain
     */
    introduction?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof ApplyMain
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof ApplyMain
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof ApplyMain
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof ApplyMain
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof ApplyMain
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof ApplyMain
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof ApplyMain
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof ApplyMain
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof ApplyMain
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof ApplyMain
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof ApplyMain
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof ApplyMain
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof ApplyMain
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof ApplyMain
     */
    nationalstandardCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ApplyMain
     */
    remark?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ApplyMain
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof ApplyMain
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof ApplyMain
     */
    specimenName?: string | null;
}
