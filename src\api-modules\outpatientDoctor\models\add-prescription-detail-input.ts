/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方明细表增加输入参数
 *
 * @export
 * @interface AddPrescriptionDetailInput
 */
export interface AddPrescriptionDetailInput {

    /**
     * 处方主表Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    prescriptionId?: number | null;

    /**
     * 药品Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    drugId?: number | null;

    /**
     * 药品编码
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    drugCode?: string | null;

    /**
     * 药品名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    drugName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    quantity?: number | null;

    /**
     * 单次量
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    singleDose?: number | null;

    /**
     * 单次量单位
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    singleDoseUnit?: string | null;

    /**
     * 给药途径Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    medicationRoutesId?: number | null;

    /**
     * 给药途径名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    medicationRoutesName?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    frequencyName?: string | null;

    /**
     * 用药天数
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    medicationDays?: number | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    amount?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    manufacturer?: string | null;

    /**
     * 药房Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    pharmacyId?: number | null;

    /**
     * 药房名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    pharmacyName?: string | null;

    /**
     * 组标志
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    groupFlag?: string | null;

    /**
     * 组号
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    groupNo?: string | null;

    /**
     * 药品限制标志
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    drugLimitFlag?: number | null;

    /**
     * 药品待发标志
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    drugPendingFlag?: number | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    chargeCategoryId?: number | null;

    /**
     * 剂量单位
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    dosageUnit?: string | null;

    /**
     * 剂量值
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    dosageValue?: number | null;

    /**
     * 含量
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    contentValue?: number | null;

    /**
     * 含量单位
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    contentUnit?: string | null;

    /**
     * 门诊包装数量
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    outpatientPackageQuantity?: number | null;

    /**
     * 最小包装单位
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    minPackageUnit?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof AddPrescriptionDetailInput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof AddPrescriptionDetailInput
     */
    refundTime?: Date | null;

    /**
     * 库存零售价
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    inventorySalePrice?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof AddPrescriptionDetailInput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 用药方式 1治疗用药 2预防用药
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    medicationMethod?: number | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    medicineCode?: string | null;

    /**
     * 用法Id
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    usageId?: number | null;

    /**
     * 用法编码
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    usageCode?: string | null;

    /**
     * 用法名称
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    usageName?: string | null;

    /**
     * 是否皮试
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    isSkinTest?: number | null;

    /**
     * 皮试结果
     *
     * @type {number}
     * @memberof AddPrescriptionDetailInput
     */
    skinTestResults?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddPrescriptionDetailInput
     */
    remark?: string | null;
}
