/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddPrescriptionMainInput } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultPrescriptionMain } from '../models';
import { AdminResultSqlSugarPagedListPrescriptionMainOutput } from '../models';
import { DeletePrescriptionMainInput } from '../models';
import { PagePrescriptionMainInput } from '../models';
import { UpdatePrescriptionMainInput } from '../models';
/**
 * PrescriptionMainApi - axios parameter creator
 * @export
 */
export const PrescriptionMainApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加处方主表 ➕
         * @param {AddPrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainAddPost: async (body?: AddPrescriptionMainInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionMain/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除处方主表 ❌
         * @param {Array<DeletePrescriptionMainInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainBatchDeletePost: async (body: Array<DeletePrescriptionMainInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiPrescriptionMainBatchDeletePost.');
            }
            const localVarPath = `/api/prescriptionMain/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除处方主表 ❌
         * @param {DeletePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainDeletePost: async (body?: DeletePrescriptionMainInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionMain/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取处方主表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiPrescriptionMainDetailGet.');
            }
            const localVarPath = `/api/prescriptionMain/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询处方主表 🔖
         * @param {PagePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainPagePost: async (body?: PagePrescriptionMainInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionMain/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新处方主表 ✏️
         * @param {UpdatePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionMainUpdatePost: async (body?: UpdatePrescriptionMainInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionMain/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PrescriptionMainApi - functional programming interface
 * @export
 */
export const PrescriptionMainApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加处方主表 ➕
         * @param {AddPrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainAddPost(body?: AddPrescriptionMainInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除处方主表 ❌
         * @param {Array<DeletePrescriptionMainInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainBatchDeletePost(body: Array<DeletePrescriptionMainInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除处方主表 ❌
         * @param {DeletePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainDeletePost(body?: DeletePrescriptionMainInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取处方主表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultPrescriptionMain>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询处方主表 🔖
         * @param {PagePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainPagePost(body?: PagePrescriptionMainInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionMainOutput>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新处方主表 ✏️
         * @param {UpdatePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainUpdatePost(body?: UpdatePrescriptionMainInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PrescriptionMainApiAxiosParamCreator(configuration).apiPrescriptionMainUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PrescriptionMainApi - factory interface
 * @export
 */
export const PrescriptionMainApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加处方主表 ➕
         * @param {AddPrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainAddPost(body?: AddPrescriptionMainInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除处方主表 ❌
         * @param {Array<DeletePrescriptionMainInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainBatchDeletePost(body: Array<DeletePrescriptionMainInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除处方主表 ❌
         * @param {DeletePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainDeletePost(body?: DeletePrescriptionMainInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取处方主表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultPrescriptionMain>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询处方主表 🔖
         * @param {PagePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainPagePost(body?: PagePrescriptionMainInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionMainOutput>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新处方主表 ✏️
         * @param {UpdatePrescriptionMainInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionMainUpdatePost(body?: UpdatePrescriptionMainInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PrescriptionMainApiFp(configuration).apiPrescriptionMainUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PrescriptionMainApi - object-oriented interface
 * @export
 * @class PrescriptionMainApi
 * @extends {BaseAPI}
 */
export class PrescriptionMainApi extends BaseAPI {
    /**
     * 
     * @summary 增加处方主表 ➕
     * @param {AddPrescriptionMainInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainAddPost(body?: AddPrescriptionMainInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除处方主表 ❌
     * @param {Array<DeletePrescriptionMainInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainBatchDeletePost(body: Array<DeletePrescriptionMainInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除处方主表 ❌
     * @param {DeletePrescriptionMainInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainDeletePost(body?: DeletePrescriptionMainInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取处方主表详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultPrescriptionMain>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询处方主表 🔖
     * @param {PagePrescriptionMainInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainPagePost(body?: PagePrescriptionMainInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionMainOutput>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新处方主表 ✏️
     * @param {UpdatePrescriptionMainInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionMainApi
     */
    public async apiPrescriptionMainUpdatePost(body?: UpdatePrescriptionMainInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PrescriptionMainApiFp(this.configuration).apiPrescriptionMainUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
