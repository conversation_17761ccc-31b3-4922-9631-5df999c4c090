/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单明细输出参数
 *
 * @export
 * @interface ApplyDetailOutput
 */
export interface ApplyDetailOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    id?: number;

    /**
     * 申请主表Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    applyMainId?: number;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    registerId?: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    visitNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    patientId?: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    itemCode?: string | null;

    /**
     * 药品或单项名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    unit?: number | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    number?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    flag?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    prescriptionType?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    specimenName?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    checkCategoryName?: string | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    urgencyLevel?: number | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    nationalstandardCode?: string | null;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    createOrgName?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ApplyDetailOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ApplyDetailOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ApplyDetailOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ApplyDetailOutput
     */
    remark?: string | null;
}
