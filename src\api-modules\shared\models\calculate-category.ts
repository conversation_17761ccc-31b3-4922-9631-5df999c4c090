/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 系统核算类别表
 *
 * @export
 * @interface CalculateCategory
 */
export interface CalculateCategory {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof CalculateCategory
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CalculateCategory
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CalculateCategory
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CalculateCategory
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CalculateCategory
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CalculateCategory
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CalculateCategory
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof CalculateCategory
     */
    wubiCode?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof CalculateCategory
     */
    status: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof CalculateCategory
     */
    orderNo?: number | null;
}
