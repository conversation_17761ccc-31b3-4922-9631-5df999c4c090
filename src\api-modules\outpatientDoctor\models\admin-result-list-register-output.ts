/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RegisterOutput } from './register-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultListRegisterOutput
 */
export interface AdminResultListRegisterOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultListRegisterOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultListRegisterOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultListRegisterOutput
     */
    message?: string | null;

    /**
     * 数据
     *
     * @type {Array<RegisterOutput>}
     * @memberof AdminResultListRegisterOutput
     */
    result?: Array<RegisterOutput> | null;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultListRegisterOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultListRegisterOutput
     */
    time?: Date;
}
