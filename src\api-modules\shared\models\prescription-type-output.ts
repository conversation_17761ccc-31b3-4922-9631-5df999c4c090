/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方类型输出参数
 *
 * @export
 * @interface PrescriptionTypeOutput
 */
export interface PrescriptionTypeOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof PrescriptionTypeOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof PrescriptionTypeOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof PrescriptionTypeOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    wubiCode?: string | null;

    /**
     * 可使用的收费类别
     *
     * @type {Array<number>}
     * @memberof PrescriptionTypeOutput
     */
    chargeCategorys?: Array<number> | null;

    /**
     * 处方条目
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    prescriptionEntries?: number | null;

    /**
     * 状态
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    status?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof PrescriptionTypeOutput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    remark?: string | null;

    /**
     * 可使用的收费类别 描述
     *
     * @type {string}
     * @memberof PrescriptionTypeOutput
     */
    chargeCategorysFkDisplayName?: string | null;
}
