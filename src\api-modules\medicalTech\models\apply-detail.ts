/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单明细表
 *
 * @export
 * @interface ApplyDetail
 */
export interface ApplyDetail {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ApplyDetail
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    tenantId?: number | null;

    /**
     * 申请单明细号
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    applyDetailNo: string;

    /**
     * 申请主表Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    applyMainId: number;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    registerId: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    visitNo: string;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    patientId: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    itemCode?: string | null;

    /**
     * 项目名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    quantity?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    price?: number | null;

    /**
     * 总金额
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    flag?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    prescriptionType?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    specimenName?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    checkCategoryName?: string | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    urgencyLevel?: number | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof ApplyDetail
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof ApplyDetail
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    nationalstandardCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ApplyDetail
     */
    remark?: string | null;
}
