/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医证型输出参数
 *
 * @export
 * @interface TcmSyndromeOutput
 */
export interface TcmSyndromeOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof TcmSyndromeOutput
     */
    id?: number;

    /**
     * 中医证型编码
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    tcmSyndromeCode?: string | null;

    /**
     * 中医证型名称
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    tcmSyndromeName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    wubiCode?: string | null;

    /**
     * 版本
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof TcmSyndromeOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof TcmSyndromeOutput
     */
    orderNo?: number | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof TcmSyndromeOutput
     */
    tenantId?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof TcmSyndromeOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof TcmSyndromeOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof TcmSyndromeOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof TcmSyndromeOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof TcmSyndromeOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof TcmSyndromeOutput
     */
    isDelete?: boolean;
}
