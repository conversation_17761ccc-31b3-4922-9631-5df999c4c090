/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListCheckCategoryOutput } from './sql-sugar-paged-list-check-category-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListCheckCategoryOutput
 */
export interface AdminResultSqlSugarPagedListCheckCategoryOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListCheckCategoryOutput}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    result?: SqlSugarPagedListCheckCategoryOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListCheckCategoryOutput
     */
    time?: Date;
}
