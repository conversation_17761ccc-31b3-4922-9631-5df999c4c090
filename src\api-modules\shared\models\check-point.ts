/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查部位表
 *
 * @export
 * @interface CheckPoint
 */
export interface CheckPoint {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof CheckPoint
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CheckPoint
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CheckPoint
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CheckPoint
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CheckPoint
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CheckPoint
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CheckPoint
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CheckPoint
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CheckPoint
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof CheckPoint
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof CheckPoint
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof CheckPoint
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof CheckPoint
     */
    wubiCode?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof CheckPoint
     */
    checkCategoryId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof CheckPoint
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof CheckPoint
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof CheckPoint
     */
    remark?: string | null;
}
