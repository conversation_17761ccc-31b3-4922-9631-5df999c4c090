/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单明细增加输入参数
 *
 * @export
 * @interface AddApplyDetailInput
 */
export interface AddApplyDetailInput {

    /**
     * 申请主表Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    applyMainId?: number | null;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    registerId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    visitNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    patientId?: number | null;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    itemCode?: string | null;

    /**
     * 药品或单项名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    unit?: number | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    number?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    flag?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    prescriptionType?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    specimenName?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    checkCategoryName?: string | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    urgencyLevel?: number | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof AddApplyDetailInput
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof AddApplyDetailInput
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    nationalstandardCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddApplyDetailInput
     */
    remark?: string | null;
}
