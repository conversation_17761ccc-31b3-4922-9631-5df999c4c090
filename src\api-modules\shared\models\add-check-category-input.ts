/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查类别增加输入参数
 *
 * @export
 * @interface AddCheckCategoryInput
 */
export interface AddCheckCategoryInput {

    /**
     * 编码
     *
     * @type {string}
     * @memberof AddCheckCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddCheckCategoryInput
     */
    name: string;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof AddCheckCategoryInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof AddCheckCategoryInput
     */
    wubiCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof AddCheckCategoryInput
     */
    chargeCategoryId: number;

    /**
     * @type {StatusEnum}
     * @memberof AddCheckCategoryInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddCheckCategoryInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddCheckCategoryInput
     */
    remark?: string | null;
}
