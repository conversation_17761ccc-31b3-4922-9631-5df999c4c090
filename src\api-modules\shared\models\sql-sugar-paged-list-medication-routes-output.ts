/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedicationRoutesOutput } from './medication-routes-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListMedicationRoutesOutput
 */
export interface SqlSugarPagedListMedicationRoutesOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<MedicationRoutesOutput>}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    items?: Array<MedicationRoutesOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListMedicationRoutesOutput
     */
    hasNextPage?: boolean;
}
