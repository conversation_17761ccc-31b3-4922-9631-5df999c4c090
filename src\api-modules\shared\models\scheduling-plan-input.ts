/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 排班计划分页查询输入参数
 *
 * @export
 * @interface SchedulingPlanInput
 */
export interface SchedulingPlanInput {

    /**
     * @type {Search}
     * @memberof SchedulingPlanInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof SchedulingPlanInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof SchedulingPlanInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof SchedulingPlanInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof SchedulingPlanInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof SchedulingPlanInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof SchedulingPlanInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof SchedulingPlanInput
     */
    descStr?: string | null;

    /**
     * 门诊日期
     *
     * @type {Date}
     * @memberof SchedulingPlanInput
     */
    outpatientDate?: Date | null;

    /**
     * 科室id
     *
     * @type {number}
     * @memberof SchedulingPlanInput
     */
    deptId?: number | null;
}
