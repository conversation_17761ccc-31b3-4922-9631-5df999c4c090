/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查部位输出参数
 *
 * @export
 * @interface CheckPointOutput
 */
export interface CheckPointOutput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    id?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    wubiCode?: string | null;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别 描述
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    checkCategoryFkDisplayName?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof CheckPointOutput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    orderNo?: number | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CheckPointOutput
     */
    createTime?: Date | null;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CheckPointOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CheckPointOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CheckPointOutput
     */
    tenantId?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof CheckPointOutput
     */
    remark?: string | null;
}
