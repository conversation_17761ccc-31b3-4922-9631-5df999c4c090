/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CheckCategoryOutput } from './check-category-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListCheckCategoryOutput
 */
export interface SqlSugarPagedListCheckCategoryOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<CheckCategoryOutput>}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    items?: Array<CheckCategoryOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCheckCategoryOutput
     */
    hasNextPage?: boolean;
}
