/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 检查类别分页查询输入参数
 *
 * @export
 * @interface PageCheckCategoryInput
 */
export interface PageCheckCategoryInput {

    /**
     * @type {Search}
     * @memberof PageCheckCategoryInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageCheckCategoryInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageCheckCategoryInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageCheckCategoryInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageCheckCategoryInput
     */
    name?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageCheckCategoryInput
     */
    selectKeyList?: Array<number> | null;
}
