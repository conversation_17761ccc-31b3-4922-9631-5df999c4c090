﻿import {useBaseApi} from '/@/api/base';

// 药品产地维护接口服务
export const useDrugPlaceApi = () => {
	const baseApi = useBaseApi("drugPlace");
	return {
		// 分页查询药品产地维护
		page: baseApi.page,
		// 查看药品产地维护详细
		detail: baseApi.detail,
		// 新增药品产地维护
		add: baseApi.add,
		// 更新药品产地维护
		update: baseApi.update,
		// 设置药品产地维护状态
		setStatus: baseApi.setStatus,
		// 删除药品产地维护
		delete: baseApi.delete,
		// 批量删除药品产地维护
		batchDelete: baseApi.batchDelete,
		// 导出药品产地维护数据
		exportData: baseApi.exportData,
		// 导入药品产地维护数据
		importData: baseApi.importData,
		// 下载药品产地维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 药品产地维护实体
export interface DrugPlace {
	// 主键Id
	id: number;
	// 产地名称
	placeName: string;
	// 产地名称拼音
	placeNamePinyin: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}