/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TcmDiagnosisOutput } from './tcm-diagnosis-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListTcmDiagnosisOutput
 */
export interface SqlSugarPagedListTcmDiagnosisOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<TcmDiagnosisOutput>}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    items?: Array<TcmDiagnosisOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListTcmDiagnosisOutput
     */
    hasNextPage?: boolean;
}
