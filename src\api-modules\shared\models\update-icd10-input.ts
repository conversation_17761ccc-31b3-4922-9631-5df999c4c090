/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 
 *
 * @export
 * @interface UpdateIcd10Input
 */
export interface UpdateIcd10Input {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateIcd10Input
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateIcd10Input
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateIcd10Input
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    tenantId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    pid: number;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    wubiCode?: string | null;

    /**
     * 国临编码
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    code?: string | null;

    /**
     * 医保编码
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    medInsCode?: string | null;

    /**
     * 省医保编码
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    proMedInsCode?: string | null;

    /**
     * 层级
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    level: number;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateIcd10Input
     */
    orderNo: number;

    /**
     * @type {StatusEnum}
     * @memberof UpdateIcd10Input
     */
    status: StatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    remark?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateIcd10Input
     */
    name: string;
}
