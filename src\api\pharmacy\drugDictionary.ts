﻿import {useBaseApi} from '/@/api/base';

// 药品字典维护接口服务
export const useDrugDictionaryApi = () => {
	const baseApi = useBaseApi("drugDictionary");
	return {
		// 分页查询药品字典维护
		page: baseApi.page,
		// 查看药品字典维护详细
		detail: baseApi.detail,
		// 新增药品字典维护
		add: baseApi.add,
		// 更新药品字典维护
		update: baseApi.update,
		// 设置药品字典维护状态
		setStatus: baseApi.setStatus,
		// 删除药品字典维护
		delete: baseApi.delete,
		// 批量删除药品字典维护
		batchDelete: baseApi.batchDelete,
		// 导出药品字典维护数据
		exportData: baseApi.exportData,
		// 导入药品字典维护数据
		importData: baseApi.importData,
		// 下载药品字典维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 药品字典维护实体
export interface DrugDictionary {
	// 主键Id
	id: number;
	// 药品编码
	drugCode?: string;
	// 药品名称
	drugName?: string;
	// 药品名称拼音
	drugNamePinyin: string;
	// 通用名称
	genericName: string;
	// 通用名称拼音
	genericNamePinyin: string;
	// 产品名称
	productName: string;
	// 产品名称拼音
	productNamePinyin: string;
	// 药品类型
	drugType: string;
	// 药品分类
	drugCategory: string;
	// 药理分类
	pharmacologicalClass: string;
	// 抗生素级别
	antibacterialLevel: string;
	// 剂型
	drugForm: string;
	// 用药途径
	drugRoute: string;
	// 用药频次
	drugFreq: string;
	// ICD10编码
	icd10: string;
	// 生产企业
	manufacturerId: number;
	// 生产企业名称
	manufacturerName: string;
	// 产地
	placeOfOrigin: string;
	// 入库单位
	storageUnit: string;
	// 包装规格
	packageSpec: string;
	// 包装数量
	packageQuantity: number;
	// 最小包装单位
	minPackageUnit: string;
	// 剂量单位
	dosageUnit: string;
	// 剂量值
	dosageValue: number;
	// 含量
	contentValue: number;
	// 含量单位
	contentUnit: string;
	// 门诊规格
	outpatientSpec: string;
	// 门诊单位
	outpatientUnit: string;
	// 门诊包装数量
	outpatientPackageQuantity: number;
	// 住院规格
	inpatientSpec: string;
	// 住院单位
	inpatientUnit: string;
	// 住院包装数量
	inpatientPackageQuantity: number;
	// 采购类型
	purchaseType: string;
	// 上市许可持有人
	holder: string;
	// 进价
	purchasePrice: number;
	// 零售价
	salePrice: number;
	// 每公斤进价
	purchasePriceOfKg: number;
	// 每公斤零售价
	salePriceOfKg: number;
	// 电子监管码
	regulationCode: string;
	// 批准文号
	approvalNumber: string;
	// 优先使用
	priorityUse: string;
	// 药房货位
	pharmacyLocation: string;
	// 药库货位
	storehouseLocation: string;
	// YPID
	ypid: string;
	// 是否拆零
	isSplit: number;
	// 国家医保编码
	medicineCode: string;
	// 是否医保药品
	isMedicare: number;
	// 是否自制药
	isSelf: number;
	// 是否基本药物
	isBasic: number;
	// 是否皮试药品
	isSkinTest: number;
	// 是否国谈药
	isCountry: number;
	// 是否辅助药品
	isAssist: number;
	// 是否临采药品
	isTemporary: number;
	// 是否溶媒
	isSolvent: number;
	// 是否新冠门诊药品
	isCovid: number;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}