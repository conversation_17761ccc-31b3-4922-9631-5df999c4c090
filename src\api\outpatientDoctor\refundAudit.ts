﻿import {useBaseApi} from '/@/api/base';

// 退费审核表接口服务
export const useRefundAuditApi = () => {
	const baseApi = useBaseApi("refundAudit");
	return {
		// 分页查询退费审核表
		page: baseApi.page,
		list: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "list",
                method: 'post',
                data,
            }, cancel);
        },
		// 查看退费审核表详细
		detail: baseApi.detail,
		// 新增退费审核表
		add: baseApi.add,
		// 更新退费审核表
		update: baseApi.update,
		// 删除退费审核表
		delete: baseApi.delete,
		// 批量删除退费审核表
		batchDelete: baseApi.batchDelete,
		// 导出退费审核表数据
		exportData: baseApi.exportData,
		// 导入退费审核表数据
		importData: baseApi.importData,
		// 下载退费审核表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 退费审核表实体
export interface RefundAudit {
	// 主键Id
	id: number;
	// 退费申请ID
	applyId: number;
	// 审核时间
	auditTime: string;
	// 审核部门ID
	auditDeptId: number;
	// 审核部门编码
	auditDeptCode: string;
	// 审核部门名称
	auditDeptName: string;
	// 审核人ID
	auditUserId: number;
	// 审核人编码
	auditUserCode: string;
	// 审核人名称
	auditUserName: string;
	// 审核原因
	auditReason: string;
	// 审核状态
	auditStatus: number;
	// 创建机构ID
	createOrgId: number;
	// 创建机构名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}