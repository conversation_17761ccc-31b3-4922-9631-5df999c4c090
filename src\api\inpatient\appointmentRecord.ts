﻿import {useBaseApi} from '/@/api/base';

// 住院预约接口服务
export const useAppointmentRecordApi = () => {
	const baseApi = useBaseApi("appointmentRecord");
	return {
		// 分页查询住院预约
		page: baseApi.page,
		// 查看住院预约详细
		detail: baseApi.detail,
		// // 新增住院预约
		// add: baseApi.add,
		// // 更新住院预约
		update: baseApi.update,
		outpatientAppointment: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'outpatientAppointment',
                method: 'post',
                data
            }, cancel);
        },
		// 删除住院预约
		delete: baseApi.delete,
		// 批量删除住院预约
		batchDelete: baseApi.batchDelete,
		// 导出住院预约数据
		exportData: baseApi.exportData,
		// 导入住院预约数据
		importData: baseApi.importData,
		// 下载住院预约数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		confirm: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'confirm',
                method: 'post',
                data
            }, cancel);
        },
	}
}

// 住院预约实体
export interface AppointmentRecord {
	// 主键Id
	id: number;
	// 患者ID
	patientId: number;
	// 患者姓名
	patientName: string;
	// 预约时间
	appointmentTime: string;
	// 证件类型
	idCardType: string;
	// 证件号码
	idCardNo: string;
	// 保险号
	insuranceNo: string;
	// 预约科室ID
	deptId: number;
	// 预约科室代码
	deptCode: string;
	// 预约科室名称
	deptName: string;
	// 预约医生ID
	doctorId: number;
	// 预约医生代码
	doctorCode: string;
	// 预约医生姓名
	doctorName: string;
	// 诊断代码
	diagnosticCode: string;
	// 诊断名称
	diagnosticName: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建组织ID
	createOrgId: number;
	// 创建组织名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}