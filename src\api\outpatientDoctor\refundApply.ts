﻿import {useBaseApi} from '/@/api/base';

// 门诊退费申请接口服务
export const useRefundApplyApi = () => {
	const baseApi = useBaseApi("refundApply");
	return {
		// 分页查询门诊退费申请
		page: baseApi.page,
		// 查看门诊退费申请详细
		detail: baseApi.detail,
		// 新增门诊退费申请
		add: baseApi.add,

		// 更新门诊退费申请
		update: baseApi.update,
		// 删除门诊退费申请
		delete: baseApi.delete,
		// 批量删除门诊退费申请
		batchDelete: baseApi.batchDelete,
		// 导出门诊退费申请数据
		exportData: baseApi.exportData,
		// 导入门诊退费申请数据
		importData: baseApi.importData,
		// 下载门诊退费申请数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		//查询审核通过未退药的处方
		getUnreturnedPrescription: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getUnreturnedPrescription",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 门诊退费申请实体
export interface RefundApply {
	// 主键Id
	id: number;
	// 退费申请单号
	applyNo: string;
	// 退费申请ID
	applyId: number;
	// 退费申请类型
	applyType: string;
	// 退费申请时间
	applyTime: string;
	// 申请部门ID
	applyDeptId: number;
	// 申请部门编码
	applyDeptCode: string;
	// 申请部门名称
	applyDeptName: string;
	// 申请人ID
	applyUserId: number;
	// 申请人编码
	applyUserCode: string;
	// 申请人名称
	applyUserName: string;
	// 就诊卡号
	cardNo: string;
	// 就诊号
	visitNo: string;
	// 门诊号
	outpatientNo: string;
	// 挂号ID
	registerId: number;
	// 患者ID
	patientId: number;
	// 患者名称
	patientName: string;
	// 退费原因
	applyReason: string;
	// 当前审核流程
	currentFlowSort: number;
	// 状态
	status: number;
	// 创建机构ID
	createOrgId: number;
	// 创建机构名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}