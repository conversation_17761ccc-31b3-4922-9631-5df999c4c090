/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方主表更新输入参数
 *
 * @export
 * @interface UpdatePrescriptionMainInput
 */
export interface UpdatePrescriptionMainInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    id: number;

    /**
     * 处方号
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    prescriptionNo?: string | null;

    /**
     * 处方时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionMainInput
     */
    prescriptionTime?: Date | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    prescriptionType?: string | null;

    /**
     * 处方名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    prescriptionName?: string | null;

    /**
     * 西药处方类型
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    wstrnMdcnPrescriptionType?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    patientName?: string | null;

    /**
     * 挂号Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    registerId?: number | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    billingDeptId?: number | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生签名
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    billingDoctorSign?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionMainInput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionMainInput
     */
    refundTime?: Date | null;

    /**
     * 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    status?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    remark?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnosticName?: string | null;

    /**
     * 次诊断1编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnostic1Code?: string | null;

    /**
     * 次诊断1名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnostic1Name?: string | null;

    /**
     * 次诊断2编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnostic2Code?: string | null;

    /**
     * 次诊断2名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    diagnostic2Name?: string | null;

    /**
     * 中医诊断编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    tcmDiagnosticCode?: string | null;

    /**
     * 中医诊断名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    tcmDiagnosticName?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    isPrint?: number | null;

    /**
     * 中药付数
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    herbsQuantity?: number | null;

    /**
     * 中药煎法
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    herbsDecoction?: string | null;

    /**
     * 是否代煎
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    isDecoction?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionMainInput
     */
    printTime?: Date | null;

    /**
     * 收费主表Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionMainInput
     */
    chargeMainId?: number | null;

    /**
     * 退费发票号
     *
     * @type {string}
     * @memberof UpdatePrescriptionMainInput
     */
    refundInvoiceNumber?: string | null;
}
