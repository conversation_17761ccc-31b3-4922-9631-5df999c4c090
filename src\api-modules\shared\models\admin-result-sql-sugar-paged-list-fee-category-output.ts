/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListFeeCategoryOutput } from './sql-sugar-paged-list-fee-category-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListFeeCategoryOutput
 */
export interface AdminResultSqlSugarPagedListFeeCategoryOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListFeeCategoryOutput}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    result?: SqlSugarPagedListFeeCategoryOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListFeeCategoryOutput
     */
    time?: Date;
}
