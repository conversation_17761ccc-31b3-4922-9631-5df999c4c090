﻿import {useBaseApi} from '/@/api/base';

// 采购计划接口服务
export const useDrugPurchasePlanApi = () => {
	const baseApi = useBaseApi("drugPurchasePlan");
	return {
		// 分页查询采购计划
		page: baseApi.page,
		// 查看采购计划详细
		detail: baseApi.detail,
		// 新增采购计划
		add: baseApi.add,
		// 更新采购计划
		update: baseApi.update,
		// 删除采购计划
		delete: baseApi.delete,
		// 批量删除采购计划
		batchDelete: baseApi.batchDelete,
		// 导出采购计划数据
		exportData: baseApi.exportData,
		// 导入采购计划数据
		importData: baseApi.importData,
		// 下载采购计划数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 采购计划实体
export interface DrugPurchasePlan {
	// 主键Id
	id: number;
	// 采购计划号
	planNo: string;
	// 采购计划时间
	planTime: string;
	// 状态（0 未处理 1 处理中 2 已完成等）
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}