/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 收费项目单位表
 *
 * @export
 * @interface ChargeItemUnit
 */
export interface ChargeItemUnit {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItemUnit
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItemUnit
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItemUnit
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    tenantId?: number | null;

    /**
     * 单位编码
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    unitCode?: string | null;

    /**
     * 单位名称
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    unitName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    wubiCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeItemUnit
     */
    remark?: string | null;

    /**
     * 状态
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    status?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeItemUnit
     */
    orderNo?: number | null;
}
