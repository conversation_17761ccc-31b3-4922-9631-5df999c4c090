﻿import { useBaseApi } from '/@/api/base';
import { service, cancelRequest } from '/@/utils/request';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
// 入库管理接口服务
export const useStorageInRecordApi = () => {
	const request = <T>(config: AxiosRequestConfig<T>, cancel: boolean = false) => {
		if (cancel) {
			cancelRequest(config.url || '');
			return Promise.resolve({} as AxiosResponse<any, any>);
		}
		return service(config);
	};
	const baseApi = useBaseApi('storageInRecord');
	return {
		// 分页查询入库管理
		page: baseApi.page,
		// 查看入库管理详细
		detail: baseApi.detail,
		// 新增入库管理
		add: baseApi.add,
		// 更新入库管理
		update: baseApi.update,
		// 删除入库管理
		delete: baseApi.delete,

		// 批量删除入库管理
		batchDelete: baseApi.batchDelete,
		// 导出入库管理数据
		exportData: baseApi.exportData,
		// 导入入库管理数据
		importData: baseApi.importData,
		// 下载入库管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		//提交
		submit: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'submit',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//审核通过
		Approve: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'approve',
					method: 'post',
					data,
				},
				cancel
			);
		},
		//审核拒绝
		Reject: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'reject',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};

// 入库管理实体
export interface StorageInRecord {
	// 主键Id
	id: number;
	// 库房
	storageId: number;
	// 库房编码
	storageCode: string;
	// 药品类型
	drugType: string;
	// 入库单号
	storageInNo: string;
	// 供应商
	supplierId: number;
	// 供应商编码
	supplierCode: string;
	// 供应商名称
	supplierName: string;
	// 入库类型
	storageInType: string;
	// 入库日期
	storageInTime: string;
	// 总进价
	totalPurchasePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 发票号
	invoiceNo: string;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}
