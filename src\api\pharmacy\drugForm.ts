﻿import {useBaseApi} from '/@/api/base';

// 药品剂型维护接口服务
export const useDrugFormApi = () => {
	const baseApi = useBaseApi("drugForm");
	return {
		// 分页查询药品剂型维护
		page: baseApi.page,
		// 查看药品剂型维护详细
		detail: baseApi.detail,
		// 新增药品剂型维护
		add: baseApi.add,
		// 更新药品剂型维护
		update: baseApi.update,
		// 设置药品剂型维护状态
		setStatus: baseApi.setStatus,
		// 删除药品剂型维护
		delete: baseApi.delete,
		// 批量删除药品剂型维护
		batchDelete: baseApi.batchDelete,
		// 导出药品剂型维护数据
		exportData: baseApi.exportData,
		// 导入药品剂型维护数据
		importData: baseApi.importData,
		// 下载药品剂型维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 药品剂型维护实体
export interface DrugForm {
	// 主键Id
	id: number;
	// 剂型名称
	formName: string;
	// 剂型名称拼音
	formNamePinyin: string;
	// 大剂型ID
	bigFormId: number;
	// 大剂型名称
	bigFormName: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}