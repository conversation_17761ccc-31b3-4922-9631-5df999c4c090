﻿import {useBaseApi} from '/@/api/base';

// 患者转介表接口服务
export const usePatientReferralApi = () => {
	const baseApi = useBaseApi("patientReferral");
	return {
		// 分页查询患者转介表
		page: baseApi.page,
		// 查看患者转介表详细
		detail: baseApi.detail,
		// 新增患者转介表
		add: baseApi.add,
		// 更新患者转介表
		update: baseApi.update,
		// 删除患者转介表
		delete: baseApi.delete,
		// 批量删除患者转介表
		batchDelete: baseApi.batchDelete,
		// 导出患者转介表数据
		exportData: baseApi.exportData,
		// 导入患者转介表数据
		importData: baseApi.importData,
		// 下载患者转介表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 患者转介表实体
export interface PatientReferral {
	// 主键Id
	id: number;
	// 患者Id
	patientId?: number;
	// 患者名称
	patientName: string;
	// 门诊号
	outpatientNo: string;
	// 转介前医生Id
	beforeDoctorId: number;
	// 转介前医生姓名
	beforeDoctorName: string;
	// 转介前科室Id
	beforeDeptId: number;
	// 转介前科室名称
	beforeDeptName: string;
	// 转介后医生Id
	afterDoctorId: number;
	// 转介后医生姓名
	afterDoctorName: string;
	// 转介后科室Id
	afterDeptId: number;
	// 转介后科室名称
	afterDeptName: string;
	// 转介时间
	referralTime: string;
	// 转介原因
	referralReason: string;
	// 转介前挂号记录Id
	beforeRegisterId: number;
	// 转介后挂号记录Id
	afterRegisterId: number;
	// 状态，默认1（有效）
	status: number;
	// 备注信息
	remark: string;
	// 创建机构Id
	createOrgId: number;
	// 创建机构名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}