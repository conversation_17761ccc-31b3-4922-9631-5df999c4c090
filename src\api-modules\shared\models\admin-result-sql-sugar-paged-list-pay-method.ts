/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListPayMethod } from './sql-sugar-paged-list-pay-method';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListPayMethod
 */
export interface AdminResultSqlSugarPagedListPayMethod {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListPayMethod}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    result?: SqlSugarPagedListPayMethod;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListPayMethod
     */
    time?: Date;
}
