/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddPrescriptionDetailInput } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultPrescriptionDetail } from '../models';
import { AdminResultSqlSugarPagedListPrescriptionDetailOutput } from '../models';
import { DeletePrescriptionDetailInput } from '../models';
import { PagePrescriptionDetailInput } from '../models';
import { UpdatePrescriptionDetailInput } from '../models';
/**
 * PrescriptionDetailApi - axios parameter creator
 * @export
 */
export const PrescriptionDetailApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加处方明细表 ➕
         * @param {AddPrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailAddPost: async (body?: AddPrescriptionDetailInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionDetail/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除处方明细表 ❌
         * @param {Array<DeletePrescriptionDetailInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailBatchDeletePost: async (body: Array<DeletePrescriptionDetailInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiPrescriptionDetailBatchDeletePost.');
            }
            const localVarPath = `/api/prescriptionDetail/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除处方明细表 ❌
         * @param {DeletePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailDeletePost: async (body?: DeletePrescriptionDetailInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionDetail/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取处方明细表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiPrescriptionDetailDetailGet.');
            }
            const localVarPath = `/api/prescriptionDetail/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询处方明细表 🔖
         * @param {PagePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailPagePost: async (body?: PagePrescriptionDetailInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionDetail/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新处方明细表 ✏️
         * @param {UpdatePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPrescriptionDetailUpdatePost: async (body?: UpdatePrescriptionDetailInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/prescriptionDetail/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PrescriptionDetailApi - functional programming interface
 * @export
 */
export const PrescriptionDetailApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加处方明细表 ➕
         * @param {AddPrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailAddPost(body?: AddPrescriptionDetailInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除处方明细表 ❌
         * @param {Array<DeletePrescriptionDetailInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailBatchDeletePost(body: Array<DeletePrescriptionDetailInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除处方明细表 ❌
         * @param {DeletePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailDeletePost(body?: DeletePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取处方明细表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultPrescriptionDetail>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询处方明细表 🔖
         * @param {PagePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailPagePost(body?: PagePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionDetailOutput>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新处方明细表 ✏️
         * @param {UpdatePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailUpdatePost(body?: UpdatePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PrescriptionDetailApiAxiosParamCreator(configuration).apiPrescriptionDetailUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PrescriptionDetailApi - factory interface
 * @export
 */
export const PrescriptionDetailApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加处方明细表 ➕
         * @param {AddPrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailAddPost(body?: AddPrescriptionDetailInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除处方明细表 ❌
         * @param {Array<DeletePrescriptionDetailInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailBatchDeletePost(body: Array<DeletePrescriptionDetailInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除处方明细表 ❌
         * @param {DeletePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailDeletePost(body?: DeletePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取处方明细表详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultPrescriptionDetail>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询处方明细表 🔖
         * @param {PagePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailPagePost(body?: PagePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionDetailOutput>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新处方明细表 ✏️
         * @param {UpdatePrescriptionDetailInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPrescriptionDetailUpdatePost(body?: UpdatePrescriptionDetailInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PrescriptionDetailApiFp(configuration).apiPrescriptionDetailUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PrescriptionDetailApi - object-oriented interface
 * @export
 * @class PrescriptionDetailApi
 * @extends {BaseAPI}
 */
export class PrescriptionDetailApi extends BaseAPI {
    /**
     * 
     * @summary 增加处方明细表 ➕
     * @param {AddPrescriptionDetailInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailAddPost(body?: AddPrescriptionDetailInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除处方明细表 ❌
     * @param {Array<DeletePrescriptionDetailInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailBatchDeletePost(body: Array<DeletePrescriptionDetailInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除处方明细表 ❌
     * @param {DeletePrescriptionDetailInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailDeletePost(body?: DeletePrescriptionDetailInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取处方明细表详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultPrescriptionDetail>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询处方明细表 🔖
     * @param {PagePrescriptionDetailInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailPagePost(body?: PagePrescriptionDetailInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListPrescriptionDetailOutput>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新处方明细表 ✏️
     * @param {UpdatePrescriptionDetailInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PrescriptionDetailApi
     */
    public async apiPrescriptionDetailUpdatePost(body?: UpdatePrescriptionDetailInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PrescriptionDetailApiFp(this.configuration).apiPrescriptionDetailUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
