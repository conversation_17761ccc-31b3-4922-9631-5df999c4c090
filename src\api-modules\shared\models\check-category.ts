/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 检查类别表
 *
 * @export
 * @interface CheckCategory
 */
export interface CheckCategory {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof CheckCategory
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CheckCategory
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CheckCategory
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CheckCategory
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CheckCategory
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CheckCategory
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CheckCategory
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CheckCategory
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CheckCategory
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof CheckCategory
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof CheckCategory
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof CheckCategory
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof CheckCategory
     */
    wubiCode?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof CheckCategory
     */
    chargeCategoryId?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof CheckCategory
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof CheckCategory
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof CheckCategory
     */
    remark?: string | null;
}
