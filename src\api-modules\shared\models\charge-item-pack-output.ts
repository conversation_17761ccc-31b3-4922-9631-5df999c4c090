/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface ChargeItemPackOutput
 */
export interface ChargeItemPackOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeItemPackOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeItemPackOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeItemPackOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeItemPackOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeItemPackOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    tenantId?: number | null;

    /**
     * 套餐Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    packId?: number | null;

    /**
     * 收费项目Id
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    chargeItemId?: number | null;

    /**
     * 收费项目数量
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    chargeItemQuantity?: number | null;

    /**
     * 单项编码
     *
     * @type {string}
     * @memberof ChargeItemPackOutput
     */
    code?: string | null;

    /**
     * 单项名称
     *
     * @type {string}
     * @memberof ChargeItemPackOutput
     */
    name?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof ChargeItemPackOutput
     */
    price?: number | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof ChargeItemPackOutput
     */
    unit?: string | null;
}
