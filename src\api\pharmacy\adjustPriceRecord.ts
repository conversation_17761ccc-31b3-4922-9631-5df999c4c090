﻿import {useBaseApi} from '/@/api/base';

// 药品调价记录接口服务
export const useAdjustPriceRecordApi = () => {
	const baseApi = useBaseApi("adjustPriceRecord");
	return {
		// 分页查询药品调价记录
		page: baseApi.page,
		// 查看药品调价记录详细
		detail: baseApi.detail,
		// 新增药品调价记录
		add: baseApi.add,
		// 更新药品调价记录
		update: baseApi.update,
		// 删除药品调价记录
		delete: baseApi.delete,
		// 批量删除药品调价记录
		batchDelete: baseApi.batchDelete,
		// 导出药品调价记录数据
		exportData: baseApi.exportData,
		// 导入药品调价记录数据
		importData: baseApi.importData,
		// 下载药品调价记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		submit: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'submit',
                method: 'post',
                data
            }, cancel);
        },
	}
}

// 药品调价记录实体
export interface AdjustPriceRecord {
	// 主键Id
	id: number;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 数量
	quantity: number;
	// 旧零售价
	oldSalePrice: number;
	// 新零售价
	newSalePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 批号
	batchNo: string;
	// 生产日期
	productionDate: string;
	// 有效期
	expirationDate: string;
	// 批准文号
	approvalNumber: string;
	// 国家医保编码
	medicineCode: string;
	// 生产厂商ID
	manufacturerId: number;
	// 生产厂商名称
	manufacturerName: string;
	// 调价时间
	adjustTime: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}