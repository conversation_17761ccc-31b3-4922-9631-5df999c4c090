/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Icd10 } from './icd10';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListIcd10
 */
export interface SqlSugarPagedListIcd10 {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListIcd10
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListIcd10
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListIcd10
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListIcd10
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<Icd10>}
     * @memberof SqlSugarPagedListIcd10
     */
    items?: Array<Icd10> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListIcd10
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListIcd10
     */
    hasNextPage?: boolean;
}
