/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 处方明细表更新输入参数
 *
 * @export
 * @interface UpdatePrescriptionDetailInput
 */
export interface UpdatePrescriptionDetailInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    id: number;

    /**
     * 处方主表Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    prescriptionId?: number | null;

    /**
     * 药品Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    drugId?: number | null;

    /**
     * 药品编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    drugCode?: string | null;

    /**
     * 药品名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    drugName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    unit?: string | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    quantity?: number | null;

    /**
     * 单次量
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    singleDose?: number | null;

    /**
     * 单次量单位
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    singleDoseUnit?: string | null;

    /**
     * 给药途径Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    medicationRoutesId?: number | null;

    /**
     * 给药途径名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    medicationRoutesName?: string | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    frequencyName?: string | null;

    /**
     * 用药天数
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    medicationDays?: number | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    amount?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    manufacturer?: string | null;

    /**
     * 药房Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    pharmacyId?: number | null;

    /**
     * 药房名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    pharmacyName?: string | null;

    /**
     * 组标志
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    groupFlag?: string | null;

    /**
     * 组号
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    groupNo?: string | null;

    /**
     * 药品限制标志
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    drugLimitFlag?: number | null;

    /**
     * 药品待发标志
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    drugPendingFlag?: number | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    chargeCategoryId?: number | null;

    /**
     * 剂量单位
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    dosageUnit?: string | null;

    /**
     * 剂量值
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    dosageValue?: number | null;

    /**
     * 含量
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    contentValue?: number | null;

    /**
     * 含量单位
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    contentUnit?: string | null;

    /**
     * 门诊包装数量
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    outpatientPackageQuantity?: number | null;

    /**
     * 最小包装单位
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    minPackageUnit?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionDetailInput
     */
    chargeTime?: Date | null;

    /**
     * 退费人员Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    refundStaffId?: number | null;

    /**
     * 退费时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionDetailInput
     */
    refundTime?: Date | null;

    /**
     * 库存零售价
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    inventorySalePrice?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof UpdatePrescriptionDetailInput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 用药方式 1治疗用药 2预防用药
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    medicationMethod?: number | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    medicineCode?: string | null;

    /**
     * 用法Id
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    usageId?: number | null;

    /**
     * 用法编码
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    usageCode?: string | null;

    /**
     * 用法名称
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    usageName?: string | null;

    /**
     * 是否皮试
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    isSkinTest?: number | null;

    /**
     * 皮试结果
     *
     * @type {number}
     * @memberof UpdatePrescriptionDetailInput
     */
    skinTestResults?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdatePrescriptionDetailInput
     */
    remark?: string | null;
}
