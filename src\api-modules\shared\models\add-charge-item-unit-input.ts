/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费项目单位增加输入参数
 *
 * @export
 * @interface AddChargeItemUnitInput
 */
export interface AddChargeItemUnitInput {

    /**
     * 单位名称
     *
     * @type {string}
     * @memberof AddChargeItemUnitInput
     */
    unitName: string;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddChargeItemUnitInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddChargeItemUnitInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddChargeItemUnitInput
     */
    orderNo?: number | null;
}
