﻿import {useBaseApi} from '/@/api/base';

// 预交金建议接口服务
export const useAdvancePaymentSuggestionApi = () => {
	const baseApi = useBaseApi("advancePaymentSuggestion");
	return {
		// 分页查询预交金建议
		page: baseApi.page,
		// 查看预交金建议详细
		detail: baseApi.detail,
		// 新增预交金建议
		add: baseApi.add,
		// 更新预交金建议
		update: baseApi.update,
		// 设置预交金建议状态
		setStatus: baseApi.setStatus,
		// 删除预交金建议
		delete: baseApi.delete,
		// 批量删除预交金建议
		batchDelete: baseApi.batchDelete,
		// 导出预交金建议数据
		exportData: baseApi.exportData,
		// 导入预交金建议数据
		importData: baseApi.importData,
		// 下载预交金建议数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 预交金建议实体
export interface AdvancePaymentSuggestion {
	// 主键Id
	id: number;
	// 诊断编码
	diagnosisCode: string;
	// 诊断名称
	diagnosisName: string;
	// 病种名称
	diseaseTypeName: string;
	// 职工
	employee: number;
	// 居民
	resident: number;
	// 自费
	selfFunded: number;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 排序
	orderNo: number;
	// 租户Id
	tenantId: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}