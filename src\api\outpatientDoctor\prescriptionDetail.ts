﻿import {useBaseApi} from '/@/api/base';

// 处方明细表接口服务
export const usePrescriptionDetailApi = () => {
	const baseApi = useBaseApi("prescriptionDetail");
	return {
		// 分页查询处方明细表
		page: baseApi.page,
		// 查看处方明细表详细
		detail: baseApi.detail,
		// 新增处方明细表
		add: baseApi.add,
		// 更新处方明细表
		update: baseApi.update,
		// 删除处方明细表
		delete: baseApi.delete,
		// 批量删除处方明细表
		batchDelete: baseApi.batchDelete,
		// 导出处方明细表数据
		exportData: baseApi.exportData,
		// 导入处方明细表数据
		importData: baseApi.importData,
		// 下载处方明细表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 处方明细表实体
export interface PrescriptionDetail {
	// 主键Id
	id: number;
	// 处方主表Id
	prescriptionId: number;
	// 药品Id
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 数量
	quantity: number;
	// 单次量
	singleDose: number;
	// 单次量单位
	singleDoseUnit: string;
	// 给药途径Id
	medicationRoutesId: number;
	// 给药途径名称
	medicationRoutesName: string;
	// 频次Id
	frequencyId: number;
	// 频次名称
	frequencyName: string;
	// 用药天数
	medicationDays: string;
	// 单价
	price: number;
	// 金额
	amount: number;
	// 生产厂家
	manufacturer: string;
	// 药房Id
	pharmacyId: number;
	// 药房名称
	pharmacyName: string;
	// 组标志
	groupFlag: string;
	// 组号
	groupNo: string;
	// 药品限制标志
	drugLimitFlag: string;
	// 药品待发标志
	drugPendingFlag: string;
	// 收费类别Id
	chargeCategoryId: number;
	// 剂量单位
	dosageUnit: string;
	// 剂量值
	dosageValue: number;
	// 含量
	contentValue: number;
	// 含量单位
	contentUnit: string;
	// 门诊包装数量
	outpatientPackageQuantity: number;
	// 最小包装单位
	minPackageUnit: string;
	// 收费人员Id
	chargeStaffId: number;
	// 收费时间
	chargeTime: string;
	// 退费人员Id
	refundStaffId: number;
	// 退费时间
	refundTime: string;
	// 库存零售价
	inventorySalePrice: number;
	// 自付比例
	selfPayRatio: number;
	// 自付比例是否审核 1审核 2不审核
	isRatioAudit: number;
	// 自付比例审核时间
	ratioAuditTime: string;
	// 自付比例审核人Id
	ratioAuditStaffId: number;
	// 自付比例审核人名称
	ratioAuditStaffName: string;
	// 用药方式 1治疗用药 2预防用药
	medicationMethod: string;
	// 国家医保编码
	medicineCode: string;
	// 用法Id
	usageId: number;
	// 用法编码
	usageCode: string;
	// 用法名称
	usageName: string;
	// 是否皮试
	isSkinTest: number;
	// 皮试结果
	skinTestResults: number;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}