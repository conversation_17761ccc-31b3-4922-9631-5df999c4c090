/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 系统疾病编码
 *
 * @export
 * @interface Icd10
 */
export interface Icd10 {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof Icd10
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof Icd10
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof Icd10
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof Icd10
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof Icd10
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof Icd10
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof Icd10
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof Icd10
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof Icd10
     */
    tenantId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof Icd10
     */
    pid: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof Icd10
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof Icd10
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof Icd10
     */
    wubiCode?: string | null;

    /**
     * 国临编码
     *
     * @type {string}
     * @memberof Icd10
     */
    code?: string | null;

    /**
     * 医保编码
     *
     * @type {string}
     * @memberof Icd10
     */
    medInsCode?: string | null;

    /**
     * 省医保编码
     *
     * @type {string}
     * @memberof Icd10
     */
    proMedInsCode?: string | null;

    /**
     * 层级
     *
     * @type {number}
     * @memberof Icd10
     */
    level: number;

    /**
     * 排序
     *
     * @type {number}
     * @memberof Icd10
     */
    orderNo: number;

    /**
     * @type {StatusEnum}
     * @memberof Icd10
     */
    status: StatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof Icd10
     */
    remark?: string | null;
}
