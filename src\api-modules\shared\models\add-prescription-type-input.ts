/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 处方类型增加输入参数
 *
 * @export
 * @interface AddPrescriptionTypeInput
 */
export interface AddPrescriptionTypeInput {

    /**
     * 名称
     *
     * @type {string}
     * @memberof AddPrescriptionTypeInput
     */
    name: string;

    /**
     * 可使用的收费类别
     *
     * @type {Array<number>}
     * @memberof AddPrescriptionTypeInput
     */
    chargeCategorys?: Array<number> | null;

    /**
     * 处方条目
     *
     * @type {number}
     * @memberof AddPrescriptionTypeInput
     */
    prescriptionEntries?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof AddPrescriptionTypeInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddPrescriptionTypeInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddPrescriptionTypeInput
     */
    remark?: string | null;
}
