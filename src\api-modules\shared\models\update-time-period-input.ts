/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 排班时间段更新输入参数
 *
 * @export
 * @interface UpdateTimePeriodInput
 */
export interface UpdateTimePeriodInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateTimePeriodInput
     */
    id: number;

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof UpdateTimePeriodInput
     */
    timePeriodName: string;

    /**
     * 开始时间
     *
     * @type {string}
     * @memberof UpdateTimePeriodInput
     */
    startTime: string;

    /**
     * 结束时间
     *
     * @type {string}
     * @memberof UpdateTimePeriodInput
     */
    endTime: string;

    /**
     * 时间段
     *
     * @type {string}
     * @memberof UpdateTimePeriodInput
     */
    timePeriodDetail?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateTimePeriodInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateTimePeriodInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateTimePeriodInput
     */
    remark?: string | null;
}
