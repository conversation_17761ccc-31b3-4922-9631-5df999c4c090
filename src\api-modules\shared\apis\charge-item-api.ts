/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddChargeItemInput } from '../models';
import { AdminResultBoolean } from '../models';
import { AdminResultChargeItem } from '../models';
import { AdminResultDictionaryStringObject } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListChargeItem } from '../models';
import { AdminResultListChargeItemDto } from '../models';
import { AdminResultSqlSugarPagedListChargeItemOutput } from '../models';
import { ChargeItemListInput } from '../models';
import { DeleteChargeItemInput } from '../models';
import { DropdownDataChargeItemInput } from '../models';
import { PageChargeItemInput } from '../models';
import { SetChargeItemStatusInput } from '../models';
import { UpdateChargeItemInput } from '../models';
/**
 * ChargeItemApi - axios parameter creator
 * @export
 */
export const ChargeItemApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 添加收费项目数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemAddChargeItemPost: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/addChargeItem`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 增加收费项目 ➕
         * @param {AddChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemAddPost: async (body?: AddChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除收费项目 ❌
         * @param {Array<DeleteChargeItemInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemBatchDeletePost: async (body: Array<DeleteChargeItemInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiChargeItemBatchDeletePost.');
            }
            const localVarPath = `/api/chargeItem/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据条件查询收费项目列表
         * @param {ChargeItemListInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemChargeItemListPost: async (body?: ChargeItemListInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/chargeItemList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除收费项目 ❌
         * @param {DeleteChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemDeletePost: async (body?: DeleteChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取收费项目详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiChargeItemDetailGet.');
            }
            const localVarPath = `/api/chargeItem/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取收费项目详情（支持批量查询）
         * @param {Array<number>} [body] 收费项目ID集合
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemDetailsPost: async (body?: Array<number>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/details`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemDropdownDataPost: async (body?: DropdownDataChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/dropdownData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出收费项目记录 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemExportPost: async (body?: PageChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载收费项目数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemImportGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入收费项目记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemImportPostForm: async (file?: Blob, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/import`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取收费项目列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询收费项目 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemPagePost: async (body?: PageChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置收费项目状态 🚫
         * @param {SetChargeItemStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemSetStatusPost: async (body?: SetChargeItemStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新收费项目 ✏️
         * @param {UpdateChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiChargeItemUpdatePost: async (body?: UpdateChargeItemInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/chargeItem/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ChargeItemApi - functional programming interface
 * @export
 */
export const ChargeItemApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 添加收费项目数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemAddChargeItemPost(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultBoolean>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemAddChargeItemPost(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 增加收费项目 ➕
         * @param {AddChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemAddPost(body?: AddChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除收费项目 ❌
         * @param {Array<DeleteChargeItemInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemBatchDeletePost(body: Array<DeleteChargeItemInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据条件查询收费项目列表
         * @param {ChargeItemListInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemChargeItemListPost(body?: ChargeItemListInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemChargeItemListPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除收费项目 ❌
         * @param {DeleteChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDeletePost(body?: DeleteChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取收费项目详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultChargeItem>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取收费项目详情（支持批量查询）
         * @param {Array<number>} [body] 收费项目ID集合
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDetailsPost(body?: Array<number>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListChargeItemDto>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemDetailsPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDropdownDataPost(body?: DropdownDataChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDictionaryStringObject>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemDropdownDataPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出收费项目记录 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemExportPost(body?: PageChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 下载收费项目数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemImportGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemImportGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导入收费项目记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemImportPostForm(file, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取收费项目列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListChargeItem>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询收费项目 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemPagePost(body?: PageChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置收费项目状态 🚫
         * @param {SetChargeItemStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemSetStatusPost(body?: SetChargeItemStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新收费项目 ✏️
         * @param {UpdateChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUpdatePost(body?: UpdateChargeItemInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ChargeItemApiAxiosParamCreator(configuration).apiChargeItemUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ChargeItemApi - factory interface
 * @export
 */
export const ChargeItemApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 添加收费项目数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemAddChargeItemPost(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultBoolean>> {
            return ChargeItemApiFp(configuration).apiChargeItemAddChargeItemPost(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 增加收费项目 ➕
         * @param {AddChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemAddPost(body?: AddChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return ChargeItemApiFp(configuration).apiChargeItemAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除收费项目 ❌
         * @param {Array<DeleteChargeItemInput>} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemBatchDeletePost(body: Array<DeleteChargeItemInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return ChargeItemApiFp(configuration).apiChargeItemBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据条件查询收费项目列表
         * @param {ChargeItemListInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemChargeItemListPost(body?: ChargeItemListInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>> {
            return ChargeItemApiFp(configuration).apiChargeItemChargeItemListPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除收费项目 ❌
         * @param {DeleteChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDeletePost(body?: DeleteChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取收费项目详情 ℹ️
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultChargeItem>> {
            return ChargeItemApiFp(configuration).apiChargeItemDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取收费项目详情（支持批量查询）
         * @param {Array<number>} [body] 收费项目ID集合
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDetailsPost(body?: Array<number>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListChargeItemDto>> {
            return ChargeItemApiFp(configuration).apiChargeItemDetailsPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取下拉列表数据 🔖
         * @param {DropdownDataChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemDropdownDataPost(body?: DropdownDataChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDictionaryStringObject>> {
            return ChargeItemApiFp(configuration).apiChargeItemDropdownDataPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出收费项目记录 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemExportPost(body?: PageChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载收费项目数据导入模板 ⬇️
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemImportGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemImportGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入收费项目记录 💾
         * @param {Blob} [file] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemImportPostForm(file?: Blob, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemImportPostForm(file, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取收费项目列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListChargeItem>> {
            return ChargeItemApiFp(configuration).apiChargeItemListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询收费项目 🔖
         * @param {PageChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemPagePost(body?: PageChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>> {
            return ChargeItemApiFp(configuration).apiChargeItemPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置收费项目状态 🚫
         * @param {SetChargeItemStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemSetStatusPost(body?: SetChargeItemStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新收费项目 ✏️
         * @param {UpdateChargeItemInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiChargeItemUpdatePost(body?: UpdateChargeItemInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ChargeItemApiFp(configuration).apiChargeItemUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ChargeItemApi - object-oriented interface
 * @export
 * @class ChargeItemApi
 * @extends {BaseAPI}
 */
export class ChargeItemApi extends BaseAPI {
    /**
     * 
     * @summary 添加收费项目数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemAddChargeItemPost(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultBoolean>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemAddChargeItemPost(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 增加收费项目 ➕
     * @param {AddChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemAddPost(body?: AddChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除收费项目 ❌
     * @param {Array<DeleteChargeItemInput>} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemBatchDeletePost(body: Array<DeleteChargeItemInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据条件查询收费项目列表
     * @param {ChargeItemListInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemChargeItemListPost(body?: ChargeItemListInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemChargeItemListPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除收费项目 ❌
     * @param {DeleteChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemDeletePost(body?: DeleteChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取收费项目详情 ℹ️
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultChargeItem>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取收费项目详情（支持批量查询）
     * @param {Array<number>} [body] 收费项目ID集合
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemDetailsPost(body?: Array<number>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListChargeItemDto>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemDetailsPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取下拉列表数据 🔖
     * @param {DropdownDataChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemDropdownDataPost(body?: DropdownDataChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDictionaryStringObject>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemDropdownDataPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出收费项目记录 🔖
     * @param {PageChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemExportPost(body?: PageChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 下载收费项目数据导入模板 ⬇️
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemImportGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemImportGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导入收费项目记录 💾
     * @param {Blob} [file] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemImportPostForm(file?: Blob, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemImportPostForm(file, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取收费项目列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListChargeItem>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询收费项目 🔖
     * @param {PageChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemPagePost(body?: PageChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListChargeItemOutput>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置收费项目状态 🚫
     * @param {SetChargeItemStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemSetStatusPost(body?: SetChargeItemStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新收费项目 ✏️
     * @param {UpdateChargeItemInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ChargeItemApi
     */
    public async apiChargeItemUpdatePost(body?: UpdateChargeItemInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ChargeItemApiFp(this.configuration).apiChargeItemUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
