import { useBaseApi } from '/@/api/base';

/**
 * 门诊收费接口服务
 */
export const useOutpatientChargeApi = () => {
	const baseApi = useBaseApi('OutpatientCharge');
	return {
		// 分页查询收费记录
		page: baseApi.page,
		/**
		 * 查询患者所有项目列表
		 */
		getItems: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getItems',
					method: 'post',
					data,
				},
				cancel
			);
		},
		/**
		 * 执行收费操作
		 */
		charge: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'charge',
					method: 'post',
					data,
				},
				cancel
			);
		},
	};
};
