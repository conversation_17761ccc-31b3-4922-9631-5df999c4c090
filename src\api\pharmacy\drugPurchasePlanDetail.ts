﻿import {useBaseApi} from '/@/api/base';

// 采购计划明细表接口服务
export const useDrugPurchasePlanDetailApi = () => {
	const baseApi = useBaseApi("drugPurchasePlanDetail");
	return {
		// 分页查询采购计划明细表
		page: baseApi.page,
		// 查看采购计划明细表详细
		detail: baseApi.detail,
		// 新增采购计划明细表
		add: baseApi.add,
		// 更新采购计划明细表
		update: baseApi.update,
		// 删除采购计划明细表
		delete: baseApi.delete,
		// 批量删除采购计划明细表
		batchDelete: baseApi.batchDelete,
		// 导出采购计划明细表数据
		exportData: baseApi.exportData,
		// 导入采购计划明细表数据
		importData: baseApi.importData,
		// 下载采购计划明细表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 采购计划明细表实体
export interface DrugPurchasePlanDetail {
	// 主键Id
	id: number;
	// 采购计划ID
	planId: number;
	// 药品ID
	drugId: number;
	// 药品编码
	drugCode: string;
	// 药品名称
	drugName: string;
	// 药品类型
	drugType: string;
	// 规格
	spec: string;
	// 单位
	unit: string;
	// 药店库存数量
	pharmacyQuantity: number;
	// 药库库存数量
	storageQuantity: number;
	// 当前销售数量
	currentSaleQuantity: number;
	// 上次销售数量
	lastSaleQuantity: number;
	// 平均销售数量
	averageSaleQuantity: number;
	// 计划采购数量
	quantity: number;
	// 采购单价
	purchasePrice: number;
	// 总采购价
	totalPurchasePrice: number;
	// 生产厂家
	manufacturerId: number;
	// 生产厂家名称
	manufacturerName: string;
	// 供应商ID
	supplierId: number;
	// 供应商名称
	supplierName: string;
	// 状态（0 未处理 1 处理中 2 已完成等）
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}