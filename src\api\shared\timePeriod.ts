﻿import {useBaseApi} from '/@/api/base';

// 排班时间段接口服务
export const useTimePeriodApi = () => {
	const baseApi = useBaseApi("timePeriod");
	return {
		// 分页查询排班时间段
		page: baseApi.page,
		// 查看排班时间段详细
		detail: baseApi.detail,
		// 新增排班时间段
		add: baseApi.add,
		// 更新排班时间段
		update: baseApi.update,
		// 设置排班时间段状态
		setStatus: baseApi.setStatus,
		// 删除排班时间段
		delete: baseApi.delete,
		// 批量删除排班时间段
		batchDelete: baseApi.batchDelete,
		// 导出排班时间段数据
		exportData: baseApi.exportData,
		// 导入排班时间段数据
		importData: baseApi.importData,
		// 下载排班时间段数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 排班时间段实体
export interface TimePeriod {
	// 主键Id
	id: number;
	// 时间段编码
	timePeriodCode: string;
	// 时间段名称
	timePeriodName?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 开始时间
	startTime?: string;
	// 结束时间
	endTime?: string;
	// 时间段
	timePeriodDetail: string;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}