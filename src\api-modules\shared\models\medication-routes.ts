/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 给药途径表
 *
 * @export
 * @interface MedicationRoutes
 */
export interface MedicationRoutes {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof MedicationRoutes
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof MedicationRoutes
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof MedicationRoutes
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof MedicationRoutes
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof MedicationRoutes
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof MedicationRoutes
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof MedicationRoutes
     */
    tenantId?: number | null;

    /**
     * 途径编码
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    routeCode?: string | null;

    /**
     * 途径名称
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    routeName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    wubiCode?: string | null;

    /**
     * 缩写
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    abbreviation?: string | null;

    /**
     * 分类
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    routeCategory?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof MedicationRoutes
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof MedicationRoutes
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof MedicationRoutes
     */
    orderNo?: number | null;
}
