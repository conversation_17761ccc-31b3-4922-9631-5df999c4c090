﻿import {useBaseApi} from '/@/api/base';

// 费用类别接口服务
export const useFeeCategoryApi = () => {
	const baseApi = useBaseApi("feeCategory");
	return {
		// 分页查询费用类别
		page: baseApi.page,
		// 查看费用类别详细
		detail: baseApi.detail,
		// 新增费用类别
		add: baseApi.add,
		// 更新费用类别
		update: baseApi.update,
		// 设置费用类别状态
		setStatus: baseApi.setStatus,
		// 删除费用类别
		delete: baseApi.delete,
		// 批量删除费用类别
		batchDelete: baseApi.batchDelete,
		// 导出费用类别数据
		exportData: baseApi.exportData,
		// 导入费用类别数据
		importData: baseApi.importData,
		// 下载费用类别数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 费用类别实体
export interface FeeCategory {
	// 主键Id
	id: number;
	// 编号
	code: string;
	// 名称
	name?: string;
	// 拼音码
	pinyinCode: string;
	// 五笔码
	wubiCode: string;
	// 医疗类别
	medCategory?: number;
	// 使用范围
	usageScope?: number;
	// 医保id
	medInsId: string;
	// 医保类型
	medInsType: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 备注
	remark: string;
}