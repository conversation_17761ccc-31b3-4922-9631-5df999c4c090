/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ChargeItemOutput } from './charge-item-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListChargeItemOutput
 */
export interface SqlSugarPagedListChargeItemOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ChargeItemOutput>}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    items?: Array<ChargeItemOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListChargeItemOutput
     */
    hasNextPage?: boolean;
}
