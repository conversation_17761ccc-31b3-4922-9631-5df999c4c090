/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Filter } from './filter';
import { Search } from './search';
 /**
 * 检查部位分页查询输入参数
 *
 * @export
 * @interface PageCheckPointInput
 */
export interface PageCheckPointInput {

    /**
     * @type {Search}
     * @memberof PageCheckPointInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof PageCheckPointInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof PageCheckPointInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof PageCheckPointInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    descStr?: string | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof PageCheckPointInput
     */
    name?: string | null;

    /**
     * 选中主键列表
     *
     * @type {Array<number>}
     * @memberof PageCheckPointInput
     */
    selectKeyList?: Array<number> | null;
}
