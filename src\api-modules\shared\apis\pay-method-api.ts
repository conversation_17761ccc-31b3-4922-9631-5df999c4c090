/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddPayMethodInput } from '../models';
import { AdminResultListPayMethod } from '../models';
import { AdminResultSqlSugarPagedListPayMethod } from '../models';
import { DeletePayMethodInput } from '../models';
import { PayMethodInput } from '../models';
import { SetStatusPayMethodInput } from '../models';
import { UpdatePayMethodInput } from '../models';
/**
 * PayMethodApi - axios parameter creator
 * @export
 */
export const PayMethodApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加系统支付方式
         * @param {AddPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodAddPost: async (body?: AddPayMethodInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除系统支付方式
         * @param {DeletePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodDeletePost: async (body?: DeletePayMethodInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取支付类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询系统支付方式
         * @param {PayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodPagePost: async (body?: PayMethodInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodSetStatusPost: async (body?: SetStatusPayMethodInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新系统支付方式
         * @param {UpdatePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPayMethodUpdatePost: async (body?: UpdatePayMethodInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payMethod/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PayMethodApi - functional programming interface
 * @export
 */
export const PayMethodApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加系统支付方式
         * @param {AddPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodAddPost(body?: AddPayMethodInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除系统支付方式
         * @param {DeletePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodDeletePost(body?: DeletePayMethodInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取支付类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListPayMethod>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询系统支付方式
         * @param {PayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodPagePost(body?: PayMethodInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListPayMethod>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodSetStatusPost(body?: SetStatusPayMethodInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新系统支付方式
         * @param {UpdatePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodUpdatePost(body?: UpdatePayMethodInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PayMethodApiAxiosParamCreator(configuration).apiPayMethodUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PayMethodApi - factory interface
 * @export
 */
export const PayMethodApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加系统支付方式
         * @param {AddPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodAddPost(body?: AddPayMethodInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PayMethodApiFp(configuration).apiPayMethodAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除系统支付方式
         * @param {DeletePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodDeletePost(body?: DeletePayMethodInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PayMethodApiFp(configuration).apiPayMethodDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取支付类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListPayMethod>> {
            return PayMethodApiFp(configuration).apiPayMethodListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询系统支付方式
         * @param {PayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodPagePost(body?: PayMethodInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListPayMethod>> {
            return PayMethodApiFp(configuration).apiPayMethodPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置状态
         * @param {SetStatusPayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodSetStatusPost(body?: SetStatusPayMethodInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PayMethodApiFp(configuration).apiPayMethodSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新系统支付方式
         * @param {UpdatePayMethodInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPayMethodUpdatePost(body?: UpdatePayMethodInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PayMethodApiFp(configuration).apiPayMethodUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PayMethodApi - object-oriented interface
 * @export
 * @class PayMethodApi
 * @extends {BaseAPI}
 */
export class PayMethodApi extends BaseAPI {
    /**
     * 
     * @summary 增加系统支付方式
     * @param {AddPayMethodInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodAddPost(body?: AddPayMethodInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PayMethodApiFp(this.configuration).apiPayMethodAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除系统支付方式
     * @param {DeletePayMethodInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodDeletePost(body?: DeletePayMethodInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PayMethodApiFp(this.configuration).apiPayMethodDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取支付类型列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListPayMethod>> {
        return PayMethodApiFp(this.configuration).apiPayMethodListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询系统支付方式
     * @param {PayMethodInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodPagePost(body?: PayMethodInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListPayMethod>> {
        return PayMethodApiFp(this.configuration).apiPayMethodPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置状态
     * @param {SetStatusPayMethodInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodSetStatusPost(body?: SetStatusPayMethodInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PayMethodApiFp(this.configuration).apiPayMethodSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新系统支付方式
     * @param {UpdatePayMethodInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PayMethodApi
     */
    public async apiPayMethodUpdatePost(body?: UpdatePayMethodInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PayMethodApiFp(this.configuration).apiPayMethodUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
