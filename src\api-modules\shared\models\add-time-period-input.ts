/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 排班时间段增加输入参数
 *
 * @export
 * @interface AddTimePeriodInput
 */
export interface AddTimePeriodInput {

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof AddTimePeriodInput
     */
    timePeriodName: string;

    /**
     * 开始时间
     *
     * @type {string}
     * @memberof AddTimePeriodInput
     */
    startTime: string;

    /**
     * 结束时间
     *
     * @type {string}
     * @memberof AddTimePeriodInput
     */
    endTime: string;

    /**
     * 时间段
     *
     * @type {string}
     * @memberof AddTimePeriodInput
     */
    timePeriodDetail?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddTimePeriodInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddTimePeriodInput
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddTimePeriodInput
     */
    remark?: string | null;
}
