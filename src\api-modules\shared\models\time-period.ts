/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 排班时间段表
 *
 * @export
 * @interface TimePeriod
 */
export interface TimePeriod {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof TimePeriod
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof TimePeriod
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof TimePeriod
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof TimePeriod
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof TimePeriod
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof TimePeriod
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof TimePeriod
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof TimePeriod
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof TimePeriod
     */
    tenantId?: number | null;

    /**
     * 时间段编码
     *
     * @type {string}
     * @memberof TimePeriod
     */
    timePeriodCode?: string | null;

    /**
     * 时间段名称
     *
     * @type {string}
     * @memberof TimePeriod
     */
    timePeriodName?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof TimePeriod
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof TimePeriod
     */
    wubiCode?: string | null;

    /**
     * 开始时间
     *
     * @type {string}
     * @memberof TimePeriod
     */
    startTime?: string | null;

    /**
     * 结束时间
     *
     * @type {string}
     * @memberof TimePeriod
     */
    endTime?: string | null;

    /**
     * 时间段
     *
     * @type {string}
     * @memberof TimePeriod
     */
    timePeriodDetail?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof TimePeriod
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof TimePeriod
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof TimePeriod
     */
    remark?: string | null;
}
