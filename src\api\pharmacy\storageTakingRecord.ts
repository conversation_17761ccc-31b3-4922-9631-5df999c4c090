﻿import {useBaseApi} from '/@/api/base';

// 药品盘点接口服务
export const useStorageTakingRecordApi = () => {
	const baseApi = useBaseApi("storageTakingRecord");
	return {
		// 分页查询药品盘点
		page: baseApi.page,
		// 查看药品盘点详细
		detail: baseApi.detail,
		// 新增药品盘点
		add: baseApi.add,
		// 更新药品盘点
		update: baseApi.update,
		// 删除药品盘点
		delete: baseApi.delete,
		// 批量删除药品盘点
		batchDelete: baseApi.batchDelete,
		// 导出药品盘点数据
		exportData: baseApi.exportData,
		// 导入药品盘点数据
		importData: baseApi.importData,
		// 下载药品盘点数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		submit: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'submit',
                method: 'post',
                data
            }, cancel);
        },
	}
}

// 药品盘点实体
export interface StorageTakingRecord {
	// 主键Id
	id: number;
	// 盘点单号
	takingNo: string;
	// 盘点时间
	takingTime: string;
	// 盘点结果
	takingResult: number;
	// 库房
	storageId: number;
	// 库房编码
	storageCode: string;
	// 库房名称
	storageName: string;
	// 现有数量
	currentQuantity: number;
	// 盘点数量
	takingQuantity: number;
	// 现有零售价
	currentSalePrice: number;
	// 盘点零售价
	takingSalePrice: number;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}