/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddCardInfoInput } from '../models';
import { AdminResultCardInfoDto } from '../models';
import { AdminResultCardInfoOutput } from '../models';
import { AdminResultSqlSugarPagedListCardInfoOutput } from '../models';
import { CardInfoInput } from '../models';
import { CardPayInput } from '../models';
import { CardRechargeInput } from '../models';
import { CardRefundCardInfoInput } from '../models';
import { LossCardInfoInput } from '../models';
import { RestoreCardInfoInput } from '../models';
import { UpdateCardInfoInput } from '../models';
/**
 * CardInfoApi - axios parameter creator
 * @export
 */
export const CardInfoApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加就诊卡
         * @param {AddCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoAddPost: async (body?: AddCardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据身份证号或者卡号获取就诊卡信息
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoCardInfoByIdOrCardNoPost: async (body?: CardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/cardInfoByIdOrCardNo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 卡缴费
         * @param {CardPayInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoCardPayPost: async (body?: CardPayInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/cardPay`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 卡充值
         * @param {CardRechargeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoCardRechargePost: async (body?: CardRechargeInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/cardRecharge`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 退卡
         * @param {CardRefundCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoCardRefundPost: async (body?: CardRefundCardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/cardRefund`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取就诊卡信息
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoDetailIdGet: async (id?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/detail/{id}`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 挂失
         * @param {LossCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoLossPost: async (body?: LossCardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/loss`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询就诊卡
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoPagePost: async (body?: CardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 恢复
         * @param {RestoreCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoRestorePost: async (body?: RestoreCardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/restore`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新就诊卡
         * @param {UpdateCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCardInfoUpdatePost: async (body?: UpdateCardInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cardInfo/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CardInfoApi - functional programming interface
 * @export
 */
export const CardInfoApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加就诊卡
         * @param {AddCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoAddPost(body?: AddCardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据身份证号或者卡号获取就诊卡信息
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardInfoByIdOrCardNoPost(body?: CardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultCardInfoOutput>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoCardInfoByIdOrCardNoPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 卡缴费
         * @param {CardPayInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardPayPost(body?: CardPayInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoCardPayPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 卡充值
         * @param {CardRechargeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardRechargePost(body?: CardRechargeInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoCardRechargePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 退卡
         * @param {CardRefundCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardRefundPost(body?: CardRefundCardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoCardRefundPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取就诊卡信息
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoDetailIdGet(id?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultCardInfoDto>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoDetailIdGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 挂失
         * @param {LossCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoLossPost(body?: LossCardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoLossPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询就诊卡
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoPagePost(body?: CardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListCardInfoOutput>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 恢复
         * @param {RestoreCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoRestorePost(body?: RestoreCardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoRestorePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新就诊卡
         * @param {UpdateCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoUpdatePost(body?: UpdateCardInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CardInfoApiAxiosParamCreator(configuration).apiCardInfoUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * CardInfoApi - factory interface
 * @export
 */
export const CardInfoApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加就诊卡
         * @param {AddCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoAddPost(body?: AddCardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据身份证号或者卡号获取就诊卡信息
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardInfoByIdOrCardNoPost(body?: CardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultCardInfoOutput>> {
            return CardInfoApiFp(configuration).apiCardInfoCardInfoByIdOrCardNoPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 卡缴费
         * @param {CardPayInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardPayPost(body?: CardPayInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoCardPayPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 卡充值
         * @param {CardRechargeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardRechargePost(body?: CardRechargeInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoCardRechargePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 退卡
         * @param {CardRefundCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoCardRefundPost(body?: CardRefundCardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoCardRefundPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取就诊卡信息
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoDetailIdGet(id?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultCardInfoDto>> {
            return CardInfoApiFp(configuration).apiCardInfoDetailIdGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 挂失
         * @param {LossCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoLossPost(body?: LossCardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoLossPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询就诊卡
         * @param {CardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoPagePost(body?: CardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListCardInfoOutput>> {
            return CardInfoApiFp(configuration).apiCardInfoPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 恢复
         * @param {RestoreCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoRestorePost(body?: RestoreCardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoRestorePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新就诊卡
         * @param {UpdateCardInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCardInfoUpdatePost(body?: UpdateCardInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CardInfoApiFp(configuration).apiCardInfoUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CardInfoApi - object-oriented interface
 * @export
 * @class CardInfoApi
 * @extends {BaseAPI}
 */
export class CardInfoApi extends BaseAPI {
    /**
     * 
     * @summary 增加就诊卡
     * @param {AddCardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoAddPost(body?: AddCardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据身份证号或者卡号获取就诊卡信息
     * @param {CardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoCardInfoByIdOrCardNoPost(body?: CardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultCardInfoOutput>> {
        return CardInfoApiFp(this.configuration).apiCardInfoCardInfoByIdOrCardNoPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 卡缴费
     * @param {CardPayInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoCardPayPost(body?: CardPayInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoCardPayPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 卡充值
     * @param {CardRechargeInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoCardRechargePost(body?: CardRechargeInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoCardRechargePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 退卡
     * @param {CardRefundCardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoCardRefundPost(body?: CardRefundCardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoCardRefundPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取就诊卡信息
     * @param {number} [id] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoDetailIdGet(id?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultCardInfoDto>> {
        return CardInfoApiFp(this.configuration).apiCardInfoDetailIdGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 挂失
     * @param {LossCardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoLossPost(body?: LossCardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoLossPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询就诊卡
     * @param {CardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoPagePost(body?: CardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListCardInfoOutput>> {
        return CardInfoApiFp(this.configuration).apiCardInfoPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 恢复
     * @param {RestoreCardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoRestorePost(body?: RestoreCardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoRestorePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新就诊卡
     * @param {UpdateCardInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CardInfoApi
     */
    public async apiCardInfoUpdatePost(body?: UpdateCardInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CardInfoApiFp(this.configuration).apiCardInfoUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
