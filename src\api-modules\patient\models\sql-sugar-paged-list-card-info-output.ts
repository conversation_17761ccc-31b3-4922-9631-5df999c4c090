/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardInfoOutput } from './card-info-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListCardInfoOutput
 */
export interface SqlSugarPagedListCardInfoOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<CardInfoOutput>}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    items?: Array<CardInfoOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListCardInfoOutput
     */
    hasNextPage?: boolean;
}
