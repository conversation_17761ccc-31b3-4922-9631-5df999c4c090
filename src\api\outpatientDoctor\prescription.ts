﻿import {useBaseApi} from '/@/api/base';

// 处方主表接口服务
export const usePrescriptionApi = () => {
	const baseApi = useBaseApi("prescription");
	return {
		// 分页查询处方主表
		page: baseApi.page,
		// 查看处方主表详细
		get:  function (id: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "get",
                method: 'get',
                data : { id },
            }, cancel);
        },
		getDetail:  function (id: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "getDetail",
                method: 'get',
                data : { id },
            }, cancel);
        },
		// 新增处方主表
		add: baseApi.add,
		// 更新处方主表
		update: baseApi.update,
		// 删除处方主表
		delete: baseApi.delete,
		deleteDetail:  function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "deleteDetail",
                method: 'post',
                data : data,
            }, cancel);
        },
		// 处方计费
        charge:  function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "charge",
                method: 'post',
                data : data,
            }, cancel);
        },
		
		// 批量删除处方主表
		batchDelete: baseApi.batchDelete,
		// 导出处方主表数据
		exportData: baseApi.exportData,
		// 导入处方主表数据
		importData: baseApi.importData,
		// 下载处方主表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
        ListOfPatient: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "listOfPatient",
                method: 'post',
                data,
            }, cancel);
        },
		//发药查询处方
		ListOfSendDrug: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "listOfSendDrug",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 处方主表实体
export interface PrescriptionMain {
	// 主键Id
	id: number;
	// 处方号
	prescriptionNo: string;
	// 处方时间
	prescriptionTime: string;
	// 处方类型
	prescriptionType: string;
	// 处方名称
	prescriptionName: string;
	// 西药处方类型
	wstrnMdcnPrescriptionType: string;
	// 患者Id
	patientId: number;
	// 患者姓名
	patientName: string;
	// 挂号Id
	registerId: number;
	// 开单科室Id
	billingDeptId: number;
	// 开单医生Id
	billingDoctorId: number;
	// 开单医生签名
	billingDoctorSign: string;
	// 收费人员Id
	chargeStaffId: number;
	// 收费时间
	chargeTime: string;
	// 退费人员Id
	refundStaffId: number;
	// 退费时间
	refundTime: string;
	// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
	status: number;
	// 备注
	remark: string;
	// 诊断编码
	diagnosticCode: string;
	// 诊断名称
	diagnosticName: string;
	// 次诊断1编码
	diagnostic1Code: string;
	// 次诊断1名称
	diagnostic1Name: string;
	// 次诊断2编码
	diagnostic2Code: string;
	// 次诊断2名称
	diagnostic2Name: string;
	// 中医诊断编码
	tcmDiagnosticCode: string;
	// 中医诊断名称
	tcmDiagnosticName: string;
	// 是否打印
	isPrint: number;
	// 中药付数
	herbsQuantity: number;
	// 中药煎法
	herbsDecoction: string;
	// 是否代煎
	isDecoction: number;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
	// 打印时间
	printTime: string;
	// 收费主表Id
	chargeMainId: number;
	// 退费发票号
	refundInvoiceNumber: string;
}