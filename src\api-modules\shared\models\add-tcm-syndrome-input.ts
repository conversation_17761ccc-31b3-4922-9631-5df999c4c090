/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 中医证型增加输入参数
 *
 * @export
 * @interface AddTcmSyndromeInput
 */
export interface AddTcmSyndromeInput {

    /**
     * 中医证型编码
     *
     * @type {string}
     * @memberof AddTcmSyndromeInput
     */
    tcmSyndromeCode: string;

    /**
     * 中医证型名称
     *
     * @type {string}
     * @memberof AddTcmSyndromeInput
     */
    tcmSyndromeName: string;

    /**
     * 版本
     *
     * @type {string}
     * @memberof AddTcmSyndromeInput
     */
    version?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddTcmSyndromeInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddTcmSyndromeInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddTcmSyndromeInput
     */
    orderNo?: number | null;
}
