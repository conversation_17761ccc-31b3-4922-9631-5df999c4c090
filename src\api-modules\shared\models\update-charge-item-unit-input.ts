/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费项目单位更新输入参数
 *
 * @export
 * @interface UpdateChargeItemUnitInput
 */
export interface UpdateChargeItemUnitInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateChargeItemUnitInput
     */
    id: number;

    /**
     * 单位名称
     *
     * @type {string}
     * @memberof UpdateChargeItemUnitInput
     */
    unitName: string;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateChargeItemUnitInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateChargeItemUnitInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateChargeItemUnitInput
     */
    orderNo?: number | null;
}
