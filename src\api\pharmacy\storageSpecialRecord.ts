﻿import {useBaseApi} from '/@/api/base';

// 特殊处理记接口服务
export const useStorageSpecialRecordApi = () => {
	const baseApi = useBaseApi("storageSpecialRecord");
	return {
		// 分页查询特殊处理记
		page: baseApi.page,
		// 查看特殊处理记详细
		detail: baseApi.detail,
		// 新增特殊处理记
		add: baseApi.add,
		// 更新特殊处理记
		update: baseApi.update,
		// 删除特殊处理记
		delete: baseApi.delete,
		// 批量删除特殊处理记
		batchDelete: baseApi.batchDelete,
		// 导出特殊处理记数据
		exportData: baseApi.exportData,
		// 导入特殊处理记数据
		importData: baseApi.importData,
		// 下载特殊处理记数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 特殊处理记实体
export interface StorageSpecialRecord {
	// 主键Id
	id: number;
	// 库房
	storageId: number;
	// 库房编码
	storageCode: string;
	// 库房名称
	storageName: string;
	// 特殊处理单号
	handleNo: string;
	// 药品类型
	drugType: string;
	// 供应商
	supplierId: number;
	// 供应商编码
	supplierCode: string;
	// 供应商名称
	supplierName: string;
	// 处理类型
	handleType: string;
	// 处理时间
	handleTime: string;
	// 总进价
	totalPurchasePrice: number;
	// 总零售价
	totalSalePrice: number;
	// 备注
	remark: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}