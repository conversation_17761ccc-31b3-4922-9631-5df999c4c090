/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListChargeItemUnitOutput } from './sql-sugar-paged-list-charge-item-unit-output';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultSqlSugarPagedListChargeItemUnitOutput
 */
export interface AdminResultSqlSugarPagedListChargeItemUnitOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    message?: string | null;

    /**
     * @type {SqlSugarPagedListChargeItemUnitOutput}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    result?: SqlSugarPagedListChargeItemUnitOutput;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultSqlSugarPagedListChargeItemUnitOutput
     */
    time?: Date;
}
