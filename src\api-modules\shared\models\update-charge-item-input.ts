/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MedServiceCategoryEnum } from './med-service-category-enum';
import { StatusEnum } from './status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 收费项目更新输入参数
 *
 * @export
 * @interface UpdateChargeItemInput
 */
export interface UpdateChargeItemInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    id: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    name: string;

    /**
     * 单位
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    unit?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    spec?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    price: number;

    /**
     * 进价
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    purchasePrice?: number | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    model?: string | null;

    /**
     * 批件产品名称
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    approvalName?: string | null;

    /**
     * 产地
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    producer?: string | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    manufacturer?: string | null;

    /**
     * 注册证号
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    registrationNumber?: string | null;

    /**
     * 物价编码
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    priceCode?: string | null;

    /**
     * 收费类别
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    chargeCategoryId: number;

    /**
     * 核算类别
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    calculateCategoryId: number;

    /**
     * 电子发票费用类别
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    dzfpChargeCategory: string;

    /**
     * 病案首页费用类别
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    basyChargeCategory: string;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateChargeItemInput
     */
    highValue?: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateChargeItemInput
     */
    useSeparately: YesNoEnum;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateChargeItemInput
     */
    uploadDw: YesNoEnum;

    /**
     * 频次
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    frequencyId?: number | null;

    /**
     * 样本类型
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    sampleType?: string | null;

    /**
     * 护理等级
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    nurseLevel?: string | null;

    /**
     * 检查类别
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    checkCategoryId?: number | null;

    /**
     * 退费模式
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    refundMode?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateChargeItemInput
     */
    _package?: YesNoEnum;

    /**
     * 使用科室
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    useDepts?: number | null;

    /**
     * @type {MedServiceCategoryEnum}
     * @memberof UpdateChargeItemInput
     */
    usageScope: MedServiceCategoryEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateChargeItemInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateChargeItemInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    orderNo?: number | null;

    /**
     * 检查部位
     *
     * @type {number}
     * @memberof UpdateChargeItemInput
     */
    checkPointId?: number | null;
}
