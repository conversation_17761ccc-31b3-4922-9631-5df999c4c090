/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApplyDetailOutput } from './apply-detail-output';
 /**
 * 分页泛型集合
 *
 * @export
 * @interface SqlSugarPagedListApplyDetailOutput
 */
export interface SqlSugarPagedListApplyDetailOutput {

    /**
     * 页码
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    page?: number;

    /**
     * 页容量
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    pageSize?: number;

    /**
     * 总条数
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    total?: number;

    /**
     * 总页数
     *
     * @type {number}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    totalPages?: number;

    /**
     * 当前页集合
     *
     * @type {Array<ApplyDetailOutput>}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    items?: Array<ApplyDetailOutput> | null;

    /**
     * 是否有上一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    hasPrevPage?: boolean;

    /**
     * 是否有下一页
     *
     * @type {boolean}
     * @memberof SqlSugarPagedListApplyDetailOutput
     */
    hasNextPage?: boolean;
}
