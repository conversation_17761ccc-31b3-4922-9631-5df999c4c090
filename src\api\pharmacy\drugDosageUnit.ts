﻿import {useBaseApi} from '/@/api/base';

// 药品剂量单位表接口服务
export const useDrugDosageUnitApi = () => {
	const baseApi = useBaseApi("drugDosageUnit");
	return {
		// 分页查询药品剂量单位表
		page: baseApi.page,
		// 查看药品剂量单位表详细
		detail: baseApi.detail,
		// 新增药品剂量单位表
		add: baseApi.add,
		// 更新药品剂量单位表
		update: baseApi.update,
		// 设置药品剂量单位表状态
		setStatus: baseApi.setStatus,
		// 删除药品剂量单位表
		delete: baseApi.delete,
		// 批量删除药品剂量单位表
		batchDelete: baseApi.batchDelete,
		// 导出药品剂量单位表数据
		exportData: baseApi.exportData,
		// 导入药品剂量单位表数据
		importData: baseApi.importData,
		// 下载药品剂量单位表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 药品剂量单位表实体
export interface DrugDosageUnit {
	// 主键Id
	id: number;
	// 剂量单位名称
	unitName: string;
	// 剂量单位名称拼音
	unitPinyin: string;
	// 状态
	status: number;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}