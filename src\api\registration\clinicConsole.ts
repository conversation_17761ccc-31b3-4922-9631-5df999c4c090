﻿import {useBaseApi} from '/@/api/base';

// 诊台维护接口服务
export const useClinicConsoleApi = () => {
	const baseApi = useBaseApi("clinicConsole");
	return {
		// 分页查询诊台维护
		page: baseApi.page,
		// 查看诊台维护详细
		detail: baseApi.detail,
		// 新增诊台维护
		add: baseApi.add,
		// 更新诊台维护
		update: baseApi.update,
		// 删除诊台维护
		delete: baseApi.delete,
		// 批量删除诊台维护
		batchDelete: baseApi.batchDelete,
		// 导出诊台维护数据
		exportData: baseApi.exportData,
		// 导入诊台维护数据
		importData: baseApi.importData,
		// 下载诊台维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 诊台维护实体
export interface ClinicConsole {
	// 主键Id
	id: number;
	// 诊台名称
	name: string;
	// 诊台编码
	code: string;
	// 诊室id
	roomId: number;
	// 诊室名称
	roomName: string;
	// 当前人数
	currentCount: number;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}