/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 收费类别表
 *
 * @export
 * @interface ChargeCategory
 */
export interface ChargeCategory {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ChargeCategory
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ChargeCategory
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ChargeCategory
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    code?: string | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    name?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    wubiCode?: string | null;

    /**
     * 提成
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    commission?: number | null;

    /**
     * 记账属性 0执行科室 1病房护士
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    accountAttribute?: number | null;

    /**
     * 类型 0药品 1非药品 2卫材
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    type?: number | null;

    /**
     * 医保类型 01药品 02检验 03检查 04治疗 05手术麻醉 06医用耗材 07服务设施 08血费 09其他
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    medInsType?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof ChargeCategory
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof ChargeCategory
     */
    orderNo?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof ChargeCategory
     */
    remark?: string | null;
}
