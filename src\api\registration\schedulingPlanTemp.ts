﻿import {useBaseApi} from '/@/api/base';

// 医生排班模板接口服务
export const useSchedulingPlanTempApi = () => {
	const baseApi = useBaseApi("schedulingPlanTemp");
	return {
		// 分页查询医生排班模板
		page: baseApi.page,
		// 查看医生排班模板详细
		detail: baseApi.detail,
		// 新增医生排班模板
		add: baseApi.add,
		// 更新医生排班模板
		update: baseApi.update,
		// 删除医生排班模板
		delete: baseApi.delete,
		// 批量删除医生排班模板
		batchDelete: baseApi.batchDelete,
		
 
	}
}

// 医生排班模板实体
export interface SchedulingPlanTemp {
	// 主键Id
	id: number;
	// 医生id
	doctorId: number;
	// 医生姓名
	doctorName: string;
	// 时间段id
	timePeriodId: number;
	// 时间段编码
	timePeriodCode: string;
	// 时间段名称
	timePeriodName: string;
	// 号别id
	regCategoryId: number;
	// 挂号类别名称
	regCategoryName: string;
	// 限号数
	regLimit: number;
	// 限预约号数
	appLimit: number;
	// 已挂号数
	regNumber: number;
	// 已预约号数
	appNumber: number;
	// 门诊日期
	outpatientDate: string;
	// 开始时间
	startTime: string;
	// 结束时间
	endTime: string;
	// 科室id
	deptId: number;
	// 科室名称
	deptName: string;
	// 星期几
	weekDay: string;
	// 诊室id
	roomId: number;
	// 诊室名称
	roomName: string;
	// ip地址
	ipAddress: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}