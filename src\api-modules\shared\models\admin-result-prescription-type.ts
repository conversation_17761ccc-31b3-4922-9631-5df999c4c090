/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PrescriptionType } from './prescription-type';
 /**
 * 全局返回结果
 *
 * @export
 * @interface AdminResultPrescriptionType
 */
export interface AdminResultPrescriptionType {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof AdminResultPrescriptionType
     */
    code?: number;

    /**
     * 类型success、warning、error
     *
     * @type {string}
     * @memberof AdminResultPrescriptionType
     */
    type?: string | null;

    /**
     * 错误信息
     *
     * @type {string}
     * @memberof AdminResultPrescriptionType
     */
    message?: string | null;

    /**
     * @type {PrescriptionType}
     * @memberof AdminResultPrescriptionType
     */
    result?: PrescriptionType;

    /**
     * 附加数据
     *
     * @type {any}
     * @memberof AdminResultPrescriptionType
     */
    extras?: any | null;

    /**
     * 时间
     *
     * @type {Date}
     * @memberof AdminResultPrescriptionType
     */
    time?: Date;
}
