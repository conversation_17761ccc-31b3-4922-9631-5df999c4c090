/* tslint:disable */
/* eslint-disable */
/**
 * MedicalTech
 * 医技管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 申请单更新输入参数
 *
 * @export
 * @interface UpdateApplyMainInput
 */
export interface UpdateApplyMainInput {

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    id: number;

    /**
     * 申请单号
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    applyNo: string;

    /**
     * 就诊Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    registerId: number;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    visitNo: string;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    patientId: number;

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    itemId?: number | null;

    /**
     * 项目编号
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    itemCode?: string | null;

    /**
     * 药品或单项名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    itemName?: string | null;

    /**
     * 规格
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    spec?: string | null;

    /**
     * 单位
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    unit?: number | null;

    /**
     * 数量
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    number?: number | null;

    /**
     * 生产厂家
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    manufacturer?: string | null;

    /**
     * 型号
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    model?: string | null;

    /**
     * 单价
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    price?: number | null;

    /**
     * 金额
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    amount?: number | null;

    /**
     * 是否套餐 1是 2否
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isPackage?: number | null;

    /**
     * 开单时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    billingTime?: Date | null;

    /**
     * 开单科室Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    billingDeptId?: number | null;

    /**
     * 开单科室名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    billingDeptName?: string | null;

    /**
     * 开单医生Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    billingDoctorId?: number | null;

    /**
     * 开单医生名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    billingDoctorName?: string | null;

    /**
     * 执行时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    executeTime?: Date | null;

    /**
     * 执行科室Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    executeDeptId?: number | null;

    /**
     * 执行科室名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    executeDeptName?: string | null;

    /**
     * 执行科室地址
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    executeDeptAddress?: string | null;

    /**
     * 执行医生Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    executeDoctorId?: number | null;

    /**
     * 执行医生名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    executeDoctorName?: string | null;

    /**
     * 收费人员Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    chargeStaffId?: number | null;

    /**
     * 收费人员名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    chargeStaffName?: string | null;

    /**
     * 收费时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    chargeTime?: Date | null;

    /**
     * 0 门诊 1住院
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    flag?: number | null;

    /**
     * 紧急程度 0:普通,1:急,2:明晨急
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    urgencyLevel?: number | null;

    /**
     * 是否婴儿
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isBaby?: number | null;

    /**
     * 婴儿姓名
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    babyName?: string | null;

    /**
     * 处方类型
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    prescriptionType?: string | null;

    /**
     * 检查类别Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    checkCategoryId?: number | null;

    /**
     * 检查类别名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    checkCategoryName?: string | null;

    /**
     * 检查部位id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    checkPointId?: number | null;

    /**
     * 检查部位名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    checkPointName?: string | null;

    /**
     * 检查目的
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    checkObjective?: string | null;

    /**
     * 症状
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    symptom?: string | null;

    /**
     * 药物过敏
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    drugAllergy?: string | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    diagnosticName?: string | null;

    /**
     * 其他诊断编码
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    otherDiagnosticCode?: string | null;

    /**
     * 其他诊断名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    otherDiagnosticName?: string | null;

    /**
     * 病情介绍
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    introduction?: string | null;

    /**
     * 是否打印
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isPrint?: number | null;

    /**
     * 打印时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    printTime?: Date | null;

    /**
     * 医嘱Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    medicalAdviceId?: number | null;

    /**
     * 处方Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    prescId?: number | null;

    /**
     * 自付比例
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    selfPayRatio?: number | null;

    /**
     * 自付比例是否审核 1审核 2不审核
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isRatioAudit?: number | null;

    /**
     * 自付比例审核时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    ratioAuditTime?: Date | null;

    /**
     * 自付比例审核人Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    ratioAuditStaffId?: number | null;

    /**
     * 自付比例审核人名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    ratioAuditStaffName?: string | null;

    /**
     * 操作人Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    operatorId?: number | null;

    /**
     * 操作人姓名
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    operatorName?: string | null;

    /**
     * 操作时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    operateTime?: Date | null;

    /**
     * 预约状态(pacs回写)
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    bookedStatus?: number | null;

    /**
     * 预约时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    bookedTime?: Date | null;

    /**
     * 预约检查房间
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    bookedRoom?: string | null;

    /**
     * 预约操作人
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    bookedOperator?: string | null;

    /**
     * 预约注意事项
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    bookedPrecautions?: string | null;

    /**
     * 预约其他信息
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    bookedOtherInfo?: string | null;

    /**
     * 取消预约时间
     *
     * @type {Date}
     * @memberof UpdateApplyMainInput
     */
    cancelBookedTime?: Date | null;

    /**
     * 取消预约操作人
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    cancelBookedOperator?: string | null;

    /**
     * 是否返回报告
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isReturnReport?: number | null;

    /**
     * 是否长期执行
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    isLongTermExecution?: number | null;

    /**
     * 频次Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    frequencyId?: number | null;

    /**
     * 频次名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    frequencyName?: string | null;

    /**
     * 天数
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    days?: number | null;

    /**
     * 医生签名
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    doctorSign?: string | null;

    /**
     * 国家医保编码
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    medicineCode?: string | null;

    /**
     * 国标编码
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    nationalstandardCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    remark?: string | null;

    /**
     * 收费类别Id
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    chargeCategoryId?: number | null;

    /**
     * 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
     *
     * @type {number}
     * @memberof UpdateApplyMainInput
     */
    status?: number | null;

    /**
     * 标本名称
     *
     * @type {string}
     * @memberof UpdateApplyMainInput
     */
    specimenName?: string | null;
}
