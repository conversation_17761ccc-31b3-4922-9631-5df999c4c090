/* tslint:disable */
/* eslint-disable */
/**
 * Shared
 * 系统基础模块
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 门诊支付类型更新输入参数
 *
 * @export
 * @interface UpdatePayMethodInput
 */
export interface UpdatePayMethodInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdatePayMethodInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdatePayMethodInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdatePayMethodInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    code?: string | null;

    /**
     * 拼音码
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    pinyinCode?: string | null;

    /**
     * 五笔码
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    wubiCode?: string | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    remark?: string | null;

    /**
     * 挂号价格
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    regPrice?: number | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdatePayMethodInput
     */
    status?: StatusEnum;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdatePayMethodInput
     */
    orderNo?: number | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    name: string;

    /**
     * 类型
     *
     * @type {string}
     * @memberof UpdatePayMethodInput
     */
    type: string;
}
